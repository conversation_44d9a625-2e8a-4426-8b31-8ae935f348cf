-- Update questionnaire configurations with complete questions
-- This migration updates the existing questionnaire configs with all questions

-- Update THC Increase questionnaire with complete questions
UPDATE questionnaire_configs 
SET 
  questions = '[
    {"key": "consistency", "text": "How consistently do you use your current medication?", "type": "radio", "order": 1, "answerOptions": [
      {"value": "every-day", "label": "Every day", "score": 3},
      {"value": "most-days", "label": "Most days", "score": 2},
      {"value": "occasionally", "label": "Occasionally", "score": 1},
      {"value": "rarely", "label": "Rarely", "score": 0}
    ]},
    {"key": "dosage", "text": "What is your typical daily dosage?", "type": "radio", "order": 2, "answerOptions": [
      {"value": "less-than-0-5g", "label": "Less than 0.5g", "score": 1},
      {"value": "0-5g-1g", "label": "0.5g - 1g", "score": 2},
      {"value": "1g-2g", "label": "1g - 2g", "score": 3},
      {"value": "more-than-2g", "label": "More than 2g", "score": 4}
    ]},
    {"key": "effectiveness", "text": "How effective is your current medication?", "type": "radio", "order": 3, "answerOptions": [
      {"value": "very-effective", "label": "Very effective", "score": 1},
      {"value": "somewhat-effective", "label": "Somewhat effective", "score": 2},
      {"value": "not-very-effective", "label": "Not very effective", "score": 3},
      {"value": "not-effective", "label": "Not effective at all", "score": 4}
    ]},
    {"key": "sideEffects", "text": "Are you experiencing any side effects?", "type": "radio", "order": 4, "answerOptions": [
      {"value": "none", "label": "None", "score": 3},
      {"value": "mild", "label": "Mild", "score": 2},
      {"value": "moderate", "label": "Moderate", "score": 1},
      {"value": "severe", "label": "Severe", "score": 0}
    ]},
    {"key": "tolerance", "text": "Have you built up a tolerance?", "type": "radio", "order": 5, "answerOptions": [
      {"value": "no-tolerance", "label": "No tolerance", "score": 1},
      {"value": "slight-tolerance", "label": "Slight tolerance", "score": 2},
      {"value": "moderate-tolerance", "label": "Moderate tolerance", "score": 3},
      {"value": "high-tolerance", "label": "High tolerance", "score": 4}
    ]},
    {"key": "currentStrength", "text": "What is your current THC strength?", "type": "radio", "order": 6, "answerOptions": [
      {"value": "10-percent", "label": "10%", "score": 4},
      {"value": "15-percent", "label": "15%", "score": 3},
      {"value": "22-percent", "label": "22%", "score": 2},
      {"value": "29-percent", "label": "29%", "score": 1}
    ]},
    {"key": "medicalHistory", "text": "Do you have relevant medical history?", "type": "radio", "order": 7, "answerOptions": [
      {"value": "chronic-pain", "label": "Chronic pain", "score": 3},
      {"value": "anxiety-depression", "label": "Anxiety/Depression", "score": 2},
      {"value": "sleep-disorders", "label": "Sleep disorders", "score": 2},
      {"value": "other", "label": "Other", "score": 1},
      {"value": "none", "label": "None", "score": 0}
    ]},
    {"key": "previousExperience", "text": "Previous experience with higher THC?", "type": "radio", "order": 8, "answerOptions": [
      {"value": "yes-positive", "label": "Yes, positive experience", "score": 3},
      {"value": "yes-mixed", "label": "Yes, mixed experience", "score": 2},
      {"value": "yes-negative", "label": "Yes, negative experience", "score": 0},
      {"value": "no-experience", "label": "No previous experience", "score": 1}
    ]},
    {"key": "reasonForIncrease", "text": "Primary reason for THC increase?", "type": "radio", "order": 9, "answerOptions": [
      {"value": "insufficient-relief", "label": "Insufficient symptom relief", "score": 4},
      {"value": "tolerance-buildup", "label": "Tolerance buildup", "score": 3},
      {"value": "doctor-recommendation", "label": "Doctor recommendation", "score": 2},
      {"value": "personal-preference", "label": "Personal preference", "score": 1}
    ]},
    {"key": "currentSatisfaction", "text": "Satisfaction with current treatment?", "type": "radio", "order": 10, "answerOptions": [
      {"value": "very-unsatisfied", "label": "Very unsatisfied", "score": 4},
      {"value": "somewhat-unsatisfied", "label": "Somewhat unsatisfied", "score": 3},
      {"value": "neutral", "label": "Neutral", "score": 2},
      {"value": "satisfied", "label": "Satisfied", "score": 1},
      {"value": "very-satisfied", "label": "Very satisfied", "score": 0}
    ]},
    {"key": "lifestyleImpact", "text": "How does your condition impact daily life?", "type": "radio", "order": 11, "answerOptions": [
      {"value": "severely", "label": "Severely impacts daily activities", "score": 4},
      {"value": "moderately", "label": "Moderately impacts daily activities", "score": 3},
      {"value": "mildly", "label": "Mildly impacts daily activities", "score": 2},
      {"value": "minimally", "label": "Minimally impacts daily activities", "score": 1},
      {"value": "no-impact", "label": "No impact on daily activities", "score": 0}
    ]},
    {"key": "complianceHistory", "text": "Treatment compliance history?", "type": "radio", "order": 12, "answerOptions": [
      {"value": "excellent", "label": "Excellent compliance", "score": 3},
      {"value": "good", "label": "Good compliance", "score": 2},
      {"value": "fair", "label": "Fair compliance", "score": 1},
      {"value": "poor", "label": "Poor compliance", "score": 0}
    ]},
    {"key": "supportSystem", "text": "Do you have a support system?", "type": "radio", "order": 13, "answerOptions": [
      {"value": "strong-support", "label": "Strong family/friend support", "score": 2},
      {"value": "some-support", "label": "Some support available", "score": 1},
      {"value": "limited-support", "label": "Limited support", "score": 0}
    ]},
    {"key": "riskFactors", "text": "Any substance abuse history?", "type": "radio", "order": 14, "answerOptions": [
      {"value": "no-history", "label": "No history", "score": 3},
      {"value": "past-history-recovered", "label": "Past history, fully recovered", "score": 2},
      {"value": "past-history-managing", "label": "Past history, managing well", "score": 1},
      {"value": "current-concerns", "label": "Current concerns", "score": 0}
    ]},
    {"key": "monitoringWillingness", "text": "Willingness for regular monitoring?", "type": "radio", "order": 15, "answerOptions": [
      {"value": "very-willing", "label": "Very willing", "score": 3},
      {"value": "willing", "label": "Willing", "score": 2},
      {"value": "somewhat-willing", "label": "Somewhat willing", "score": 1},
      {"value": "reluctant", "label": "Reluctant", "score": 0}
    ]}
  ]'::jsonb,
  max_score = 61,
  threshold = 45,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'thc_increase';

-- Update Extend Treatment Plan questionnaire with complete questions
UPDATE questionnaire_configs
SET
  questions = '[
    {"key": "currentEffectiveness", "text": "How effective is your current treatment?", "type": "radio", "order": 1, "answerOptions": [
      {"value": "very-effective", "label": "Very effective", "score": 4},
      {"value": "effective", "label": "Effective", "score": 3},
      {"value": "somewhat-effective", "label": "Somewhat effective", "score": 2},
      {"value": "not-effective", "label": "Not effective", "score": 1}
    ]},
    {"key": "sideEffects", "text": "Are you experiencing side effects?", "type": "radio", "order": 2, "answerOptions": [
      {"value": "none", "label": "None", "score": 3},
      {"value": "mild", "label": "Mild", "score": 2},
      {"value": "moderate", "label": "Moderate", "score": 1},
      {"value": "severe", "label": "Severe", "score": 0}
    ]},
    {"key": "treatmentDuration", "text": "How long have you been on current treatment?", "type": "radio", "order": 3, "answerOptions": [
      {"value": "less-than-3-months", "label": "Less than 3 months", "score": 1},
      {"value": "3-6-months", "label": "3-6 months", "score": 2},
      {"value": "6-12-months", "label": "6-12 months", "score": 3},
      {"value": "more-than-12-months", "label": "More than 12 months", "score": 4}
    ]},
    {"key": "complianceRate", "text": "How consistently do you take your medication?", "type": "radio", "order": 4, "answerOptions": [
      {"value": "always", "label": "Always as prescribed", "score": 4},
      {"value": "mostly", "label": "Mostly as prescribed", "score": 3},
      {"value": "sometimes", "label": "Sometimes miss doses", "score": 2},
      {"value": "rarely", "label": "Rarely as prescribed", "score": 1}
    ]},
    {"key": "symptomImprovement", "text": "Overall symptom improvement since starting?", "type": "radio", "order": 5, "answerOptions": [
      {"value": "significant-improvement", "label": "Significant improvement", "score": 4},
      {"value": "moderate-improvement", "label": "Moderate improvement", "score": 3},
      {"value": "slight-improvement", "label": "Slight improvement", "score": 2},
      {"value": "no-improvement", "label": "No improvement", "score": 1}
    ]},
    {"key": "qualityOfLife", "text": "Impact on quality of life?", "type": "radio", "order": 6, "answerOptions": [
      {"value": "greatly-improved", "label": "Greatly improved", "score": 4},
      {"value": "improved", "label": "Improved", "score": 3},
      {"value": "slightly-improved", "label": "Slightly improved", "score": 2},
      {"value": "no-change", "label": "No change", "score": 1}
    ]},
    {"key": "functionalCapacity", "text": "Ability to perform daily activities?", "type": "radio", "order": 7, "answerOptions": [
      {"value": "much-better", "label": "Much better than before", "score": 4},
      {"value": "better", "label": "Better than before", "score": 3},
      {"value": "slightly-better", "label": "Slightly better", "score": 2},
      {"value": "same-or-worse", "label": "Same or worse", "score": 1}
    ]},
    {"key": "doctorRecommendation", "text": "Has your doctor recommended continuation?", "type": "radio", "order": 8, "answerOptions": [
      {"value": "strongly-recommended", "label": "Strongly recommended", "score": 4},
      {"value": "recommended", "label": "Recommended", "score": 3},
      {"value": "neutral", "label": "Neutral/undecided", "score": 2},
      {"value": "not-recommended", "label": "Not recommended", "score": 1}
    ]},
    {"key": "alternativeTreatments", "text": "Have you tried alternative treatments?", "type": "radio", "order": 9, "answerOptions": [
      {"value": "tried-unsuccessful", "label": "Yes, but unsuccessful", "score": 3},
      {"value": "tried-less-effective", "label": "Yes, but less effective", "score": 2},
      {"value": "not-tried", "label": "No, haven''t tried alternatives", "score": 1},
      {"value": "prefer-current", "label": "Prefer current treatment", "score": 4}
    ]},
    {"key": "treatmentGoals", "text": "Are your treatment goals being met?", "type": "radio", "order": 10, "answerOptions": [
      {"value": "fully-met", "label": "Fully met", "score": 4},
      {"value": "mostly-met", "label": "Mostly met", "score": 3},
      {"value": "partially-met", "label": "Partially met", "score": 2},
      {"value": "not-met", "label": "Not met", "score": 1}
    ]}
  ]'::jsonb,
  max_score = 42,
  threshold = 35,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'extend_tp';

-- Update Add 22% THC questionnaire with complete questions
UPDATE questionnaire_configs
SET
  questions = '[
    {"key": "reasonSideEffects", "text": "Experiencing side effects with 29%", "type": "checkbox", "order": 1, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonGentlerEffect", "text": "Want gentler effect", "type": "checkbox", "order": 2, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonDaytimeUse", "text": "Need for daytime use", "type": "checkbox", "order": 3, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonWorkFunctionality", "text": "Better work functionality", "type": "checkbox", "order": 4, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonSocialSituations", "text": "Better for social situations", "type": "checkbox", "order": 5, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonMicrodosing", "text": "Interest in microdosing", "type": "checkbox", "order": 6, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonCostEffective", "text": "More cost-effective option", "type": "checkbox", "order": 7, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonDoctorRecommendation", "text": "Doctor recommendation", "type": "checkbox", "order": 8, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonTrialPeriod", "text": "Trial period before higher strength", "type": "checkbox", "order": 9, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonAnxietyReduction", "text": "Reduce anxiety from high THC", "type": "checkbox", "order": 10, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonBetterSleep", "text": "Better sleep quality", "type": "checkbox", "order": 11, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonAppetiteControl", "text": "Better appetite control", "type": "checkbox", "order": 12, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonPainManagement", "text": "Specific pain management needs", "type": "checkbox", "order": 13, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]}
  ]'::jsonb,
  max_score = 33,
  threshold = 7,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'add_22_thc';
