-- THC Increase Questionnaire Part 2 - Remaining Questions (Steps 3-8)
-- This continues from Part 1 and adds the remaining questions

-- Add the remaining questions to complete the THC Increase questionnaire
UPDATE questionnaire_configs 
SET questions = questions || '[
  {
    "key": "sideEffect",
    "text": "Have you experienced any side effects from using 22% THC flower during the trial?",
    "type": "radio",
    "order": 8,
    "sectionId": "f3c4d5e6-a7b8-4012-cdef-345678901234",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "drowsiness",
        "label": "Drowsiness",
        "score": 1
      },
      {
        "value": "dry-mouth",
        "label": "Dry mouth",
        "score": 1
      },
      {
        "value": "dizziness",
        "label": "Dizziness",
        "score": 1
      },
      {
        "value": "increased-heart-rate",
        "label": "Increased heart rate",
        "score": 1
      },
      {
        "value": "anxiety",
        "label": "Anxiety",
        "score": 1
      },
      {
        "value": "none",
        "label": "None",
        "score": 4
      },
      {
        "value": "other",
        "label": "Other (Please specify):",
        "score": 0
      }
    ]
  },
  {
    "key": "sideEffectsOther",
    "text": "Other (Please specify)",
    "type": "text",
    "order": 9,
    "sectionId": "f3c4d5e6-a7b8-4012-cdef-345678901234",
    "contributesToScore": false,
    "dependsOnQuestion": "sideEffect",
    "textFieldConfig": {
      "required": false,
      "maxLength": 300,
      "placeholder": "Other (Please specify)"
    }
  },
  {
    "key": "sideEffectManageability",
    "text": "On a scale of 1 to 10, how manageable were these side effects?",
    "type": "radio",
    "order": 10,
    "sectionId": "f4d5e6a7-b8c9-4123-def0-456789012345",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "1-2",
        "label": "1-2",
        "score": 0
      },
      {
        "value": "3-4",
        "label": "3-4",
        "score": 1
      },
      {
        "value": "5-6",
        "label": "5-6",
        "score": 2
      },
      {
        "value": "7-8",
        "label": "7-8",
        "score": 3
      },
      {
        "value": "9-10",
        "label": "9-10",
        "score": 4
      }
    ]
  },
  {
    "key": "concerns",
    "text": "Did you have any concerns or issues with your THC flower treatment during the trial?",
    "type": "radio",
    "order": 11,
    "sectionId": "f4d5e6a7-b8c9-4123-def0-456789012345",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes",
        "label": "Yes",
        "score": 0
      },
      {
        "value": "no",
        "label": "No",
        "score": 3
      }
    ]
  },
  {
    "key": "treatmentEffectiveness",
    "text": "Was the 22% THC flower effective in treating your condition?",
    "type": "radio",
    "order": 12,
    "sectionId": "f5e6f7g8-h9i0-4234-ef01-567890123456",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-effective",
        "label": "Very effective",
        "score": 4
      },
      {
        "value": "effective",
        "label": "Effective",
        "score": 3
      },
      {
        "value": "somewhat-effective",
        "label": "Somewhat effective",
        "score": 2
      },
      {
        "value": "not-effective",
        "label": "Not effective",
        "score": 0
      }
    ]
  },
  {
    "key": "weaknessAssessment",
    "text": "Do you feel the 22% THC flower was too weak in managing your symptoms?",
    "type": "radio",
    "order": 13,
    "sectionId": "f5e6f7g8-h9i0-4234-ef01-567890123456",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes-definitely",
        "label": "Yes, definitely",
        "score": 4
      },
      {
        "value": "yes-somewhat",
        "label": "Yes, somewhat",
        "score": 3
      },
      {
        "value": "no-adequate",
        "label": "No, it was adequate",
        "score": 1
      },
      {
        "value": "no-too-strong",
        "label": "No, it was too strong",
        "score": 0
      }
    ]
  },
  {
    "key": "insufficientRelief",
    "text": "During these breakout pain or acute episodes, did you find the 22% THC flower insufficient in providing relief?",
    "type": "radio",
    "order": 14,
    "sectionId": "f6f7g8h9-i0j1-4345-f012-678901234567",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes-definitely",
        "label": "Yes, definitely",
        "score": 4
      },
      {
        "value": "yes-somewhat",
        "label": "Yes, somewhat",
        "score": 3
      },
      {
        "value": "no-adequate",
        "label": "No, it was adequate",
        "score": 1
      },
      {
        "value": "no-complete-relief",
        "label": "No, it provided complete relief",
        "score": 0
      }
    ]
  },
  {
    "key": "satisfactionWithForm",
    "text": "How satisfied are you with the form of THC flower you used during the trial (e.g., vaporized, smoked)?",
    "type": "radio",
    "order": 15,
    "sectionId": "f6f7g8h9-i0j1-4345-f012-678901234567",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-satisfied",
        "label": "Very satisfied",
        "score": 4
      },
      {
        "value": "satisfied",
        "label": "Satisfied",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unsatisfied",
        "label": "Unsatisfied",
        "score": 1
      },
      {
        "value": "very-unsatisfied",
        "label": "Very unsatisfied",
        "score": 0
      }
    ]
  },
  {
    "key": "openToHigherPotency",
    "text": "Would you be open to trying a higher potency of THC flower (29%)?",
    "type": "radio",
    "order": 16,
    "sectionId": "f7g8h9i0-j1k2-4456-0123-789012345678",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes",
        "label": "Yes",
        "score": 4
      },
      {
        "value": "no",
        "label": "No",
        "score": 0
      },
      {
        "value": "maybe",
        "label": "Maybe",
        "score": 2
      }
    ]
  },
  {
    "key": "quickReliefImportance",
    "text": "How important is it for you to have quick relief from your symptoms?",
    "type": "radio",
    "order": 17,
    "sectionId": "f7g8h9i0-j1k2-4456-0123-789012345678",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-important",
        "label": "Very important",
        "score": 4
      },
      {
        "value": "important",
        "label": "Important",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "not-important",
        "label": "Not important",
        "score": 1
      }
    ]
  },
  {
    "key": "continueTreatment",
    "text": "How likely are you to continue using cannabinoid treatments as part of your treatment plan?",
    "type": "radio",
    "order": 18,
    "sectionId": "f8h9i0j1-k2l3-4567-1234-890123456789",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-likely",
        "label": "Very likely",
        "score": 4
      },
      {
        "value": "likely",
        "label": "Likely",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unlikely",
        "label": "Unlikely",
        "score": 1
      },
      {
        "value": "very-unlikely",
        "label": "Very unlikely",
        "score": 0
      }
    ]
  },
  {
    "key": "overallSatisfaction",
    "text": "How satisfied are you with the overall experience of using 22% THC flower during the trial?",
    "type": "radio",
    "order": 19,
    "sectionId": "f8h9i0j1-k2l3-4567-1234-890123456789",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-satisfied",
        "label": "Very satisfied",
        "score": 4
      },
      {
        "value": "satisfied",
        "label": "Satisfied",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unsatisfied",
        "label": "Unsatisfied",
        "score": 1
      },
      {
        "value": "very-unsatisfied",
        "label": "Very unsatisfied",
        "score": 0
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- Final verification
SELECT 
  id, 
  name, 
  max_score, 
  threshold, 
  sections_count,
  jsonb_array_length(questions) as total_questions
FROM questionnaire_configs 
WHERE id = 'thc_increase';
