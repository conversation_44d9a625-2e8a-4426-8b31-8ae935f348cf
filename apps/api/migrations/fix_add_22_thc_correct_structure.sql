-- Fix Add 22% THC questionnaire with EXACT structure as specified
-- Using individual checkbox questions with correct format

-- First, update the main config
UPDATE questionnaire_configs 
SET 
  name = 'Add 22% THC Option',
  max_score = 33,
  threshold = 7,
  sections_count = 5,
  last_modified = NOW(),
  modified_by = 'system_migration_correct_structure'
WHERE id = 'add_22_thc';

-- Clear existing sections and recreate with proper UUIDs
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
('a1b2c3d4-e5f6-4890-abcd-ef1234567890', 'add_22_thc', 'Reasons for Request', 'Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)', 1, '{"requireAllQuestions": false, "minimumQuestionsRequired": 1}', true),
('b2c3d4e5-f6a7-4901-bcde-f23456789012', 'add_22_thc', 'Current Treatment Response', 'How well has your current 29% THC treatment worked for you?', 2, '{"requireAllQuestions": true}', true),
('c3d4e5f6-a7b8-4012-cdef-************', 'add_22_thc', 'Health Changes', 'Any changes in your health, medications, or lifestyle since your last consultation?', 3, '{"requireAllQuestions": true}', true),
('d4e5f6a7-b8c9-4123-def0-************', 'add_22_thc', 'Expectations and Preferences', 'Your expectations and preferences for the 22% THC option', 4, '{"requireAllQuestions": true}', true),
('e5f6a7b8-c9d0-4234-ef01-567890123456', 'add_22_thc', 'Consent', 'Consent for treatment plan review and update', 5, '{"requireAllQuestions": true}', true);

-- Update questions with EXACT structure as specified
UPDATE questionnaire_configs 
SET questions = '[
  {
    "key": "reasonSideEffects",
    "text": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
    "type": "checkbox",
    "order": 1,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "answerOptions": [
      {
        "label": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
        "score": 4,
        "value": "reasonSideEffects"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "reasonGentlerEffect",
    "text": "Prefer a gentler effect for daily or daytime use",
    "type": "checkbox",
    "order": 2,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "answerOptions": [
      {
        "label": "Prefer a gentler effect for daily or daytime use",
        "score": 3,
        "value": "reasonGentlerEffect"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "reasonDifferentStrain",
    "text": "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)",
    "type": "checkbox",
    "order": 3,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "answerOptions": [
      {
        "label": "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)",
        "score": 2,
        "value": "reasonDifferentStrain"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "reasonTolerance",
    "text": "Building tolerance to higher THC strain",
    "type": "checkbox",
    "order": 4,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "answerOptions": [
      {
        "label": "Building tolerance to higher THC strain",
        "score": 3,
        "value": "reasonTolerance"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "reasonOther",
    "text": "Other (please describe):",
    "type": "checkbox",
    "order": 5,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "answerOptions": [
      {
        "label": "Other (please describe):",
        "score": 1,
        "value": "reasonOther"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "reasonOtherText",
    "text": "Please describe your other reason...",
    "type": "text",
    "order": 6,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "textFieldConfig": {
      "required": false,
      "maxLength": 300,
      "placeholder": "Please describe your other reason..."
    },
    "dependsOnQuestion": "reasonOther",
    "contributesToScore": false
  },
  {
    "key": "symptomImprovement",
    "text": "Rate your symptom improvement on a scale of 1-10 (1 = no improvement, 10 = complete relief):",
    "type": "slider",
    "order": 7,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "sliderConfig": {
      "max": 10,
      "min": 1,
      "scoreMapping": {
        "1": 0,
        "2": 0,
        "3": 1,
        "4": 1,
        "5": 2,
        "6": 2,
        "7": 3,
        "8": 3,
        "9": 4,
        "10": 4
      }
    },
    "contributesToScore": true
  },
  {
    "key": "sideEffectsNone",
    "text": "None",
    "type": "checkbox",
    "order": 8,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "answerOptions": [
      {
        "label": "None",
        "score": 4,
        "value": "sideEffectsNone"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "sideEffectsMild",
    "text": "Mild (e.g., dry mouth, mild sedation, tiredness)",
    "type": "checkbox",
    "order": 9,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "answerOptions": [
      {
        "label": "Mild (e.g., dry mouth, mild sedation, tiredness)",
        "score": 2,
        "value": "sideEffectsMild"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "sideEffectsModerate",
    "text": "Moderate (e.g., dizziness, nausea, appetite changes)",
    "type": "checkbox",
    "order": 10,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "answerOptions": [
      {
        "label": "Moderate (e.g., dizziness, nausea, appetite changes)",
        "score": 1,
        "value": "sideEffectsModerate"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "sideEffectsStrong",
    "text": "Strong (e.g., anxiety, mood changes, racing heart, confusion)",
    "type": "checkbox",
    "order": 11,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "answerOptions": [
      {
        "label": "Strong (e.g., anxiety, mood changes, racing heart, confusion)",
        "score": 0,
        "value": "sideEffectsStrong"
      }
    ],
    "contributesToScore": true
  },
  {
    "key": "sideEffectsDescription",
    "text": "Please describe briefly...",
    "type": "text",
    "order": 12,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "textFieldConfig": {
      "required": true,
      "maxLength": 500,
      "placeholder": "Please describe briefly..."
    },
    "dependsOnQuestion": "sideEffectsModerate,sideEffectsStrong",
    "contributesToScore": false
  }
]'::jsonb
WHERE id = 'add_22_thc';

-- Verify the update
SELECT 'Add 22% THC questions updated (Part 1):' as status, jsonb_array_length(questions) as question_count 
FROM questionnaire_configs 
WHERE id = 'add_22_thc';
