-- Update Add 22% THC questionnaire to match exact specification
-- Based on docs/add22thc_questions_and_answers.json

-- First, update the main questionnaire config
UPDATE questionnaire_configs 
SET 
  name = 'Add 22% THC Option',
  max_score = 33,
  threshold = 7,
  sections_count = 5,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'add_22_thc';

-- Clear existing sections for this questionnaire
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

-- Insert the 5 sections as per specification using proper UUIDs
INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
('a1b2c3d4-e5f6-4890-abcd-ef1234567890', 'add_22_thc', 'Reasons for Request', 'Why do you want to add a 22% THC product to your treatment plan?', 1, '{"requireAllQuestions": false, "minimumQuestionsRequired": 1}', true),
('b2c3d4e5-f6a7-4901-bcde-f23456789012', 'add_22_thc', 'Current Treatment Response', 'How well has your current 29% THC treatment worked for you?', 2, '{"requireAllQuestions": true}', true),
('c3d4e5f6-a7b8-4012-cdef-************', 'add_22_thc', 'Health Changes', 'Any changes in your health, medications, or lifestyle since your last consultation?', 3, '{"requireAllQuestions": true}', true),
('d4e5f6a7-b8c9-4123-def0-************', 'add_22_thc', 'Expectations and Preferences', 'Your expectations and preferences for the 22% THC option', 4, '{"requireAllQuestions": true}', true),
('e5f6a7b8-c9d0-4234-ef01-************', 'add_22_thc', 'Consent', 'Consent for treatment plan review and update', 5, '{"requireAllQuestions": true}', true);

-- Update the questions array with exact specification
UPDATE questionnaire_configs 
SET questions = '[
  {
    "key": "reasonSideEffects",
    "text": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
    "type": "checkbox",
    "order": 1,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "reasonSideEffects",
        "label": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
        "score": 4
      }
    ]
  },
  {
    "key": "reasonGentlerEffect", 
    "text": "Prefer a gentler effect for daily or daytime use",
    "type": "checkbox",
    "order": 2,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "reasonGentlerEffect",
        "label": "Prefer a gentler effect for daily or daytime use",
        "score": 3
      }
    ]
  },
  {
    "key": "reasonDifferentStrain",
    "text": "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)",
    "type": "checkbox",
    "order": 3,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "reasonDifferentStrain",
        "label": "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)",
        "score": 2
      }
    ]
  },
  {
    "key": "reasonTolerance",
    "text": "Building tolerance to higher THC strain",
    "type": "checkbox",
    "order": 4,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "reasonTolerance",
        "label": "Building tolerance to higher THC strain",
        "score": 3
      }
    ]
  },
  {
    "key": "reasonOther",
    "text": "Other (please describe):",
    "type": "checkbox",
    "order": 5,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "reasonOther",
        "label": "Other (please describe):",
        "score": 1
      }
    ]
  },
  {
    "key": "reasonOtherText",
    "text": "Please describe your other reason...",
    "type": "text",
    "order": 6,
    "sectionId": "a1b2c3d4-e5f6-4890-abcd-ef1234567890",
    "contributesToScore": false,
    "dependsOnQuestion": "reasonOther",
    "textFieldConfig": {
      "required": false,
      "maxLength": 300,
      "placeholder": "Please describe your other reason..."
    }
  },
  {
    "key": "symptomImprovement",
    "text": "Rate your symptom improvement on a scale of 1-10 (1 = no improvement, 10 = complete relief):",
    "type": "slider",
    "order": 7,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "sliderConfig": {
      "min": 1,
      "max": 10,
      "scoreMapping": {
        "1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
        "6": 2, "7": 3, "8": 3, "9": 4, "10": 4
      }
    }
  },
  {
    "key": "sideEffectsNone",
    "text": "None",
    "type": "checkbox",
    "order": 8,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "sideEffectsNone",
        "label": "None",
        "score": 4
      }
    ]
  },
  {
    "key": "sideEffectsMild",
    "text": "Mild (e.g., dry mouth, mild sedation, tiredness)",
    "type": "checkbox",
    "order": 9,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "sideEffectsMild",
        "label": "Mild (e.g., dry mouth, mild sedation, tiredness)",
        "score": 2
      }
    ]
  },
  {
    "key": "sideEffectsModerate",
    "text": "Moderate (e.g., dizziness, nausea, appetite changes)",
    "type": "checkbox",
    "order": 10,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "sideEffectsModerate",
        "label": "Moderate (e.g., dizziness, nausea, appetite changes)",
        "score": 1
      }
    ]
  },
  {
    "key": "sideEffectsStrong",
    "text": "Strong (e.g., anxiety, mood changes, racing heart, confusion)",
    "type": "checkbox",
    "order": 11,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "sideEffectsStrong",
        "label": "Strong (e.g., anxiety, mood changes, racing heart, confusion)",
        "score": 0
      }
    ]
  },
  {
    "key": "sideEffectsDescription",
    "text": "Please describe briefly...",
    "type": "text",
    "order": 12,
    "sectionId": "b2c3d4e5-f6a7-4901-bcde-f23456789012",
    "contributesToScore": false,
    "dependsOnQuestion": "sideEffectsModerate,sideEffectsStrong",
    "textFieldConfig": {
      "required": true,
      "maxLength": 500,
      "placeholder": "Please describe briefly..."
    }
  },
  {
    "key": "healthChanges",
    "text": "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
    "type": "radio",
    "order": 13,
    "sectionId": "c3d4e5f6-a7b8-4012-cdef-************",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "no-changes",
        "label": "No changes",
        "score": 3
      },
      {
        "value": "yes",
        "label": "Yes — please describe:",
        "score": 1
      }
    ]
  },
  {
    "key": "healthChangesDescription",
    "text": "Please describe the changes...",
    "type": "text",
    "order": 14,
    "sectionId": "c3d4e5f6-a7b8-4012-cdef-************",
    "contributesToScore": false,
    "dependsOnQuestion": "healthChanges",
    "textFieldConfig": {
      "required": true,
      "maxLength": 400,
      "placeholder": "Please describe the changes..."
    }
  },
  {
    "key": "expectations",
    "text": "What do you hope to achieve by adding a 22% THC product? (e.g., reduced side effects, better daily functioning)",
    "type": "text",
    "order": 15,
    "sectionId": "d4e5f6a7-b8c9-4123-def0-************",
    "contributesToScore": false,
    "textFieldConfig": {
      "required": true,
      "maxLength": 400,
      "placeholder": "Please describe your expectations..."
    }
  },
  {
    "key": "concerns",
    "text": "Do you have any concerns about adding this option? (e.g., potential interactions or reduced effectiveness)",
    "type": "text",
    "order": 16,
    "sectionId": "d4e5f6a7-b8c9-4123-def0-************",
    "contributesToScore": false,
    "textFieldConfig": {
      "required": true,
      "maxLength": 400,
      "placeholder": "Please describe any concerns..."
    }
  },
  {
    "key": "usagePlan",
    "text": "How do you plan to use the 22% THC product alongside your 29%?",
    "type": "radio",
    "order": 17,
    "sectionId": "d4e5f6a7-b8c9-4123-def0-************",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "alternative-situations",
        "label": "As an alternative for specific times/situations",
        "score": 4
      },
      {
        "value": "rotation-time-symptoms",
        "label": "In rotation with 29% depending on time of day or symptoms",
        "score": 3
      },
      {
        "value": "unsure-advice",
        "label": "Unsure – would like advice from my doctor",
        "score": 2
      },
      {
        "value": "other",
        "label": "Other (please describe):",
        "score": 1
      }
    ]
  },
  {
    "key": "consent",
    "text": "Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?",
    "type": "radio",
    "order": 18,
    "sectionId": "e5f6a7b8-c9d0-4234-ef01-************",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes",
        "label": "Yes",
        "score": 5
      },
      {
        "value": "no",
        "label": "No",
        "score": 0
      }
    ]
  }
]'::jsonb
WHERE id = 'add_22_thc';

-- Verify the update
SELECT 
  id, 
  name, 
  max_score, 
  threshold, 
  sections_count,
  jsonb_array_length(questions) as question_count
FROM questionnaire_configs 
WHERE id = 'add_22_thc';
