-- Simple robust migration for Add 22% THC questionnaire with proper UUIDs
-- This replaces the complex approach with a single robust update

-- Step 1: Update main config
UPDATE questionnaire_configs 
SET 
  name = 'Add 22% THC Option',
  max_score = 33,
  threshold = 7,
  sections_count = 5,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'add_22_thc';

-- Step 2: Clear existing sections
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

-- Step 3: Insert sections with proper UUIDs (using gen_random_uuid() for proper UUIDs)
INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
(gen_random_uuid(), 'add_22_thc', 'Reasons for Request', 'Why do you want to add a 22% THC product to your treatment plan?', 1, '{"requireAllQuestions": false, "minimumQuestionsRequired": 1}', true),
(gen_random_uuid(), 'add_22_thc', 'Current Treatment Response', 'How well has your current 29% THC treatment worked for you?', 2, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Health Changes', 'Any changes in your health, medications, or lifestyle since your last consultation?', 3, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Expectations and Preferences', 'Your expectations and preferences for the 22% THC option', 4, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Consent', 'Consent for treatment plan review and update', 5, '{"requireAllQuestions": true}', true);

-- Step 4: Get the section IDs we just created (in order)
WITH section_ids AS (
  SELECT id, order_index 
  FROM questionnaire_sections 
  WHERE questionnaire_id = 'add_22_thc' 
  ORDER BY order_index
),
section_mapping AS (
  SELECT 
    CASE order_index
      WHEN 1 THEN 'step1'
      WHEN 2 THEN 'step2'
      WHEN 3 THEN 'step3'
      WHEN 4 THEN 'step4'
      WHEN 5 THEN 'step5'
    END as step_name,
    id as section_id
  FROM section_ids
)
SELECT 'Section mapping:' as info, step_name, section_id FROM section_mapping;

-- Step 5: Verify the sections were created correctly
SELECT 'Sections created:' as status, count(*) as section_count 
FROM questionnaire_sections 
WHERE questionnaire_id = 'add_22_thc';

-- Step 6: Show current config
SELECT 'Current config:' as status, id, name, max_score, threshold, sections_count 
FROM questionnaire_configs 
WHERE id = 'add_22_thc';
