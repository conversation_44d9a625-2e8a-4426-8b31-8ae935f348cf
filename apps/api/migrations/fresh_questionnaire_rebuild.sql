-- Section Description Update Migration
-- Updates section descriptions to match the revised documentation
-- The database structure is already correct, just updating descriptions

BEGIN;

-- Update section descriptions to match documentation
UPDATE questionnaire_sections SET
  description = 'Baseline usage assessment',
  last_modified = NOW()
WHERE id = '0b8fc963-6a99-4c9e-bdda-270f2d6e8ce8';

UPDATE questionnaire_sections SET
  description = 'Effectiveness and condition evaluation',
  last_modified = NOW()
WHERE id = '008d6157-a00b-4dc9-b870-e3f61bb7abc3';

UPDATE questionnaire_sections SET
  description = 'Symptom changes and side effects',
  last_modified = NOW()
WHERE id = '973d62d3-90a8-4320-8ead-6fe42d3070bd';

UPDATE questionnaire_sections SET
  description = 'Manageability and concerns',
  last_modified = NOW()
WHERE id = '01e4405a-d245-4477-b452-fd2409a7f09a';

UPDATE questionnaire_sections SET
  description = 'Overall effectiveness evaluation',
  last_modified = NOW()
WHERE id = 'faf628b3-c503-44b0-b277-dff99daeaaba';

UPDATE questionnaire_sections SET
  description = 'Current treatment satisfaction',
  last_modified = NOW()
WHERE id = 'b22df526-5d47-460a-92d7-49e67a6d3cd5';

UPDATE questionnaire_sections SET
  description = 'Openness to changes',
  last_modified = NOW()
WHERE id = 'e4ab7200-84e6-4d7d-994f-554b3f935e31';

UPDATE questionnaire_sections SET
  description = 'Long-term treatment intent',
  last_modified = NOW()
WHERE id = 'b8ea250d-d146-4191-8228-b0f7d018ae9e';

-- Update remaining section descriptions for other questionnaires
UPDATE questionnaire_sections SET
  description = 'Treatment plan adherence',
  last_modified = NOW()
WHERE id = '474aaa4f-ba55-443b-bd22-03f5976a154b';

UPDATE questionnaire_sections SET
  description = 'Why patient wants 22% option',
  last_modified = NOW()
WHERE id = 'c4df331c-35bf-4ad0-a540-5aadd74aeee7';

UPDATE questionnaire_sections SET
  description = 'How patient responds to 29% THC',
  last_modified = NOW()
WHERE id = '34d047c0-6ff0-4b32-9dcd-844cb56f2f7b';

UPDATE questionnaire_sections SET
  description = 'Reasons + strength selection + quantities',
  last_modified = NOW()
WHERE id = 'a9e927d7-1b7a-44f7-9ad7-6bb417daf291';

UPDATE questionnaire_sections SET
  description = 'Current effectiveness assessment',
  last_modified = NOW()
WHERE id = '69f02b4a-7e24-47e2-b40c-160c92e89b27';

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE '=== Section Descriptions Updated ===';
    RAISE NOTICE 'Database structure is already correct!';
    RAISE NOTICE 'Questions are properly assigned to sections.';
    RAISE NOTICE '';
    RAISE NOTICE 'The issue was in the React component - SectionManager';
    RAISE NOTICE 'The fix has been applied to properly count questions by sectionId.';
    RAISE NOTICE '';
    RAISE NOTICE 'Expected question counts per section:';
    RAISE NOTICE '- Add 22% THC Step 1: 9 questions';
    RAISE NOTICE '- Add 22% THC Step 2: 6 questions';
    RAISE NOTICE '- Add 22% THC Step 3: 2 questions';
    RAISE NOTICE '- Add 22% THC Step 4: 3 questions';
    RAISE NOTICE '- Add 22% THC Step 5: 1 question';
END $$;

COMMIT;
