-- Update THC Increase questionnaire to match exact specification
-- Based on docs/thc_increase_questions_and_answers.json

-- Step 1: Update the main questionnaire config
UPDATE questionnaire_configs 
SET 
  name = 'THC Increase (29% THC)',
  max_score = 61,
  threshold = 45,
  sections_count = 8,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'thc_increase';

-- Step 2: Clear existing sections for this questionnaire
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'thc_increase';

-- Step 3: Insert the 8 sections as per specification with proper UUIDs
INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
('f1a2b3c4-d5e6-4890-abcd-ef1234567890', 'thc_increase', 'Trial Usage Assessment', 'How consistent were you in using 22% THC flower during the two-week trial?', 1, '{"requireAllQuestions": true}', true),
('f2b3c4d5-e6a7-4901-bcde-f23456789012', 'thc_increase', 'Condition and Treatment Effectiveness', 'What condition are you treating and how effective has it been?', 2, '{"requireAllQuestions": true}', true),
('f3c4d5e6-a7b8-4012-cdef-************', 'thc_increase', 'Symptoms and Side Effects Assessment', 'Changes in symptoms and side effects experienced', 3, '{"requireAllQuestions": true}', true),
('f4d5e6a7-b8c9-4123-def0-************', 'thc_increase', 'Side Effect Management and Concerns', 'How manageable are side effects and any concerns', 4, '{"requireAllQuestions": true}', true),
('f5e6a7b8-c9d0-4234-ef01-************', 'thc_increase', 'Treatment Effectiveness Assessment', 'Overall effectiveness and strength assessment', 5, '{"requireAllQuestions": true}', true),
('f6a7b8c9-d0e1-4345-f012-678901234567', 'thc_increase', 'Relief and Treatment Satisfaction', 'Relief during episodes and satisfaction with form', 6, '{"requireAllQuestions": true}', true),
('f7b8c9d0-e1f2-4456-0123-789012345678', 'thc_increase', 'Future Treatment Preferences', 'Openness to higher potency and importance of quick relief', 7, '{"requireAllQuestions": true}', true),
('f8c9d0e1-f2a3-4567-1234-890123456789', 'thc_increase', 'Overall Treatment Experience', 'Likelihood to continue and overall satisfaction', 8, '{"requireAllQuestions": true}', true);

-- Step 4: Verify sections were created
SELECT 'Sections created:' as status, count(*) as section_count FROM questionnaire_sections WHERE questionnaire_id = 'thc_increase';

-- Step 5: Update questions array with exact specification (Part 1 - Steps 1-4)
UPDATE questionnaire_configs 
SET questions = '[
  {
    "key": "consistency",
    "text": "How consistent were you in using 22% THC flower during the two-week trial?",
    "type": "radio",
    "order": 1,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "every-day",
        "label": "Every day as prescribed",
        "score": 3
      },
      {
        "value": "most-days",
        "label": "Most days",
        "score": 2
      },
      {
        "value": "occasionally",
        "label": "Occasionally",
        "score": 1
      },
      {
        "value": "rarely",
        "label": "Rarely",
        "score": 0
      }
    ]
  },
  {
    "key": "dosage",
    "text": "What dosage of 22% THC flower were you taking during the trial?",
    "type": "radio",
    "order": 2,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "less-than-0-5g",
        "label": "Less than 0.5g/day",
        "score": 1
      },
      {
        "value": "0-5g-1g",
        "label": "0.5g - 1g/day",
        "score": 2
      },
      {
        "value": "1g-2g",
        "label": "1g - 2g/day",
        "score": 3
      },
      {
        "value": "more-than-2g",
        "label": "More than 2g/day",
        "score": 4
      }
    ]
  },
  {
    "key": "frequency",
    "text": "How often did you use 22% THC flower?",
    "type": "radio",
    "order": 3,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "once-a-day",
        "label": "Once a day",
        "score": 1
      },
      {
        "value": "twice-a-day",
        "label": "Twice a day",
        "score": 2
      },
      {
        "value": "three-times-a-day",
        "label": "Three times a day",
        "score": 3
      },
      {
        "value": "as-needed",
        "label": "As needed",
        "score": 1
      }
    ]
  },
  {
    "key": "condition",
    "text": "What condition are you using THC flower to treat?",
    "type": "radio",
    "order": 4,
    "sectionId": "f2b3c4d5-e6a7-4901-bcde-f23456789012",
    "contributesToScore": false,
    "answerOptions": [
      {
        "value": "chronic-pain",
        "label": "Chronic pain",
        "score": 0
      },
      {
        "value": "anxiety",
        "label": "Anxiety",
        "score": 0
      },
      {
        "value": "insomnia",
        "label": "Insomnia",
        "score": 0
      },
      {
        "value": "inflammation",
        "label": "Inflammation",
        "score": 0
      },
      {
        "value": "other",
        "label": "Other (Please specify):",
        "score": 0
      }
    ]
  },
  {
    "key": "conditionOther",
    "text": "Type your condition here",
    "type": "text",
    "order": 5,
    "sectionId": "f2b3c4d5-e6a7-4901-bcde-f23456789012",
    "contributesToScore": false,
    "dependsOnQuestion": "condition",
    "textFieldConfig": {
      "required": false,
      "maxLength": 300,
      "placeholder": "Type your condition here"
    }
  },
  {
    "key": "effectiveness",
    "text": "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
    "type": "radio",
    "order": 6,
    "sectionId": "f2b3c4d5-e6a7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "1-2",
        "label": "1-2",
        "score": 0
      },
      {
        "value": "3-4",
        "label": "3-4",
        "score": 1
      },
      {
        "value": "5-6",
        "label": "5-6",
        "score": 2
      },
      {
        "value": "7-8",
        "label": "7-8",
        "score": 3
      },
      {
        "value": "9-10",
        "label": "9-10",
        "score": 4
      }
    ]
  },
  {
    "key": "symptomChanges",
    "text": "Have you noticed any changes in your symptoms since starting 22% THC flower?",
    "type": "radio",
    "order": 7,
    "sectionId": "f3c4d5e6-a7b8-4012-cdef-************",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "significant-improvement",
        "label": "Significant improvement",
        "score": 4
      },
      {
        "value": "some-improvement",
        "label": "Some improvement",
        "score": 3
      },
      {
        "value": "no-change",
        "label": "No change",
        "score": 1
      },
      {
        "value": "worsening-symptoms",
        "label": "Worsening of symptoms",
        "score": 0
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- This is Part 1 of the questions (Steps 1-2).
-- Part 2 will add the remaining questions (Steps 3-8) due to size limitations.

-- Verify the update so far
SELECT
  id,
  name,
  max_score,
  threshold,
  sections_count,
  jsonb_array_length(questions) as question_count
FROM questionnaire_configs
WHERE id = 'thc_increase';
