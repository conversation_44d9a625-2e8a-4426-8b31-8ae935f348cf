-- Fix Extend TP questionnaire to match exact specification
-- Based on docs/extend_tp_questions_and_answers.json

-- Step 1: Update the main questionnaire config to match specification
UPDATE questionnaire_configs 
SET 
  name = 'Extend Treatment Plan (6-Month Extension)',
  max_score = 60,
  threshold = 42,
  sections_count = 12,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'extend_tp';

-- Step 2: Update section titles and descriptions to match specification
UPDATE questionnaire_sections 
SET 
  title = 'Treatment Adherence',
  description = 'During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 1;

UPDATE questionnaire_sections 
SET 
  title = 'Symptom Improvement',
  description = 'How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 2;

UPDATE questionnaire_sections 
SET 
  title = 'Symptom Frequency',
  description = 'How has the frequency or occurrence of your symptoms changed with treatment? (For example, how often you experience pain flare-ups, anxiety attacks, or sleepless nights now versus before.)'
WHERE questionnaire_id = 'extend_tp' AND order_index = 3;

UPDATE questionnaire_sections 
SET 
  title = 'Additional Relief Needs',
  description = 'Did you need to use any extra treatments besides the prescribed cannabis to manage your condition (such as additional medications or extra cannabis doses)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 4;

UPDATE questionnaire_sections 
SET 
  title = 'Functional Benefits',
  description = 'How has the treatment affected your daily functioning or quality of life (ability to perform work, household tasks, exercise, socialize, etc.)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 5;

UPDATE questionnaire_sections 
SET 
  title = 'Sleep Quality',
  description = 'How has your sleep quality or pattern been affected by the treatment?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 6;

UPDATE questionnaire_sections 
SET 
  title = 'Tolerance Development',
  description = 'Did you find that you needed to increase your cannabis dose over time to get the same symptom relief?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 7;

UPDATE questionnaire_sections 
SET 
  title = 'Side Effect Severity',
  description = 'Which statement best describes the side effects you experienced from the medicinal cannabis?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 8;

UPDATE questionnaire_sections 
SET 
  title = 'Side Effect Impact',
  description = 'How did any side effects impact your willingness to continue treatment?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 9;

UPDATE questionnaire_sections 
SET 
  title = 'Overall Satisfaction',
  description = 'Overall, how satisfied are you with the results of your medicinal cannabis treatment so far?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 10;

UPDATE questionnaire_sections 
SET 
  title = 'Goal Achievement',
  description = 'To what extent has this treatment met the goals or expectations you had when you started?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 11;

UPDATE questionnaire_sections 
SET 
  title = 'Future Treatment Intent',
  description = 'What would you like to do going forward after this 3-month trial?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 12;

-- Step 3: Get the current section IDs for reference
SELECT 'Current section IDs:' as info, order_index, id, title 
FROM questionnaire_sections 
WHERE questionnaire_id = 'extend_tp' 
ORDER BY order_index;

-- Step 4: Verify the updates
SELECT 'Updated config:' as status, id, name, max_score, threshold, sections_count 
FROM questionnaire_configs 
WHERE id = 'extend_tp';
