-- Migration to add section support to questionnaire system
-- This migration transforms the flat question structure to section-based organization

-- Create sections table to store section configurations
CREATE TABLE IF NOT EXISTS questionnaire_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    questionnaire_id VARCHAR(50) NOT NULL REFERENCES questionnaire_configs(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    validation_rules JSONB DEFAULT '{}', -- SectionValidationRules
    created_at TIMESTAMPTZ DEFAULT now(),
    last_modified TIMESTAMPTZ DEFAULT now(),
    
    -- Constraints
    CONSTRAINT chk_order_positive CHECK (order_index > 0),
    UNIQUE(questionnaire_id, order_index)
);

-- Add section_id to questions in the JSONB structure
-- We'll handle this through application logic since JSONB structure changes are complex

-- Create index for better performance
CREATE INDEX idx_questionnaire_sections_questionnaire_id ON questionnaire_sections(questionnaire_id);
CREATE INDEX idx_questionnaire_sections_order ON questionnaire_sections(questionnaire_id, order_index);

-- Add sections column to questionnaire_configs for easier querying
ALTER TABLE questionnaire_configs 
ADD COLUMN IF NOT EXISTS sections_count INTEGER DEFAULT 0;

-- Function to update sections count
CREATE OR REPLACE FUNCTION update_sections_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE questionnaire_configs 
        SET sections_count = (
            SELECT COUNT(*) 
            FROM questionnaire_sections 
            WHERE questionnaire_id = NEW.questionnaire_id AND is_active = true
        )
        WHERE id = NEW.questionnaire_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE questionnaire_configs 
        SET sections_count = (
            SELECT COUNT(*) 
            FROM questionnaire_sections 
            WHERE questionnaire_id = OLD.questionnaire_id AND is_active = true
        )
        WHERE id = OLD.questionnaire_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update sections count
CREATE TRIGGER trigger_update_sections_count
    AFTER INSERT OR UPDATE OR DELETE ON questionnaire_sections
    FOR EACH ROW EXECUTE FUNCTION update_sections_count();

-- Insert sections for THC Increase Questionnaire (8 sections)
INSERT INTO questionnaire_sections (questionnaire_id, title, description, order_index, validation_rules) VALUES
('thc_increase', 'Current Usage Patterns', 'Baseline usage assessment', 1, '{"requireAllQuestions": true}'),
('thc_increase', 'Treatment Assessment', 'Effectiveness and condition evaluation', 2, '{"requireAllQuestions": true}'),
('thc_increase', 'Symptom Management', 'Symptom changes and side effects', 3, '{"requireAllQuestions": true}'),
('thc_increase', 'Side Effects Assessment', 'Manageability and concerns', 4, '{"requireAllQuestions": true}'),
('thc_increase', 'Treatment Effectiveness', 'Overall effectiveness evaluation', 5, '{"requireAllQuestions": true}'),
('thc_increase', 'Relief and Satisfaction', 'Current treatment satisfaction', 6, '{"requireAllQuestions": true}'),
('thc_increase', 'Future Treatment Preferences', 'Openness to changes', 7, '{"requireAllQuestions": true}'),
('thc_increase', 'Treatment Continuation', 'Long-term treatment intent', 8, '{"requireAllQuestions": true}');

-- Insert sections for ExtendTP Questionnaire (12 sections - one question per section)
INSERT INTO questionnaire_sections (questionnaire_id, title, description, order_index, validation_rules) VALUES
('extend_tp', 'Treatment Adherence', 'Treatment plan adherence assessment', 1, '{"requireAllQuestions": true}'),
('extend_tp', 'Symptom Improvement', 'Symptom improvement evaluation', 2, '{"requireAllQuestions": true}'),
('extend_tp', 'Symptom Frequency', 'Symptom frequency changes', 3, '{"requireAllQuestions": true}'),
('extend_tp', 'Additional Relief Needs', 'Need for additional relief', 4, '{"requireAllQuestions": true}'),
('extend_tp', 'Functional Benefits', 'Functional improvement assessment', 5, '{"requireAllQuestions": true}'),
('extend_tp', 'Sleep Quality', 'Sleep quality changes', 6, '{"requireAllQuestions": true}'),
('extend_tp', 'Tolerance Development', 'Tolerance development assessment', 7, '{"requireAllQuestions": true}'),
('extend_tp', 'Side Effect Severity', 'Side effect severity evaluation', 8, '{"requireAllQuestions": true}'),
('extend_tp', 'Side Effect Impact', 'Side effect impact assessment', 9, '{"requireAllQuestions": true}'),
('extend_tp', 'Overall Satisfaction', 'Overall satisfaction evaluation', 10, '{"requireAllQuestions": true}'),
('extend_tp', 'Goal Achievement', 'Treatment goal achievement', 11, '{"requireAllQuestions": true}'),
('extend_tp', 'Future Treatment Intent', 'Future treatment intention', 12, '{"requireAllQuestions": true}');

-- Insert sections for Add 22% THC Questionnaire (5 sections)
INSERT INTO questionnaire_sections (questionnaire_id, title, description, order_index, validation_rules) VALUES
('add_22_thc', 'Reasons for Request', 'Why patient wants 22% option', 1, '{"requireAllQuestions": true}'),
('add_22_thc', 'Current Treatment Response', 'How patient responds to 29% THC', 2, '{"requireAllQuestions": true}'),
('add_22_thc', 'Health Changes Assessment', 'Recent health changes', 3, '{"requireAllQuestions": true}'),
('add_22_thc', 'Usage Planning', 'How 22% would be used', 4, '{"requireAllQuestions": true}'),
('add_22_thc', 'Consent', 'Final approval for addition', 5, '{"requireAllQuestions": true}');

-- Insert sections for Quantity Increase Questionnaire (5 sections)
INSERT INTO questionnaire_sections (questionnaire_id, title, description, order_index, validation_rules) VALUES
('quantity_increase', 'Request Details & Reasons', 'Comprehensive request assessment', 1, '{"requireAllQuestions": true}'),
('quantity_increase', 'Treatment Response', 'Current effectiveness assessment', 2, '{"requireAllQuestions": true}'),
('quantity_increase', 'Health Changes', 'Recent health status changes', 3, '{"requireAllQuestions": true}'),
('quantity_increase', 'Usage Planning', 'Expectations and intended usage', 4, '{"requireAllQuestions": true}'),
('quantity_increase', 'Consent', 'Final approval for increase', 5, '{"requireAllQuestions": true}');

-- Update sections count for all questionnaires
UPDATE questionnaire_configs SET sections_count = (
    SELECT COUNT(*) FROM questionnaire_sections 
    WHERE questionnaire_id = questionnaire_configs.id AND is_active = true
);

COMMIT;
