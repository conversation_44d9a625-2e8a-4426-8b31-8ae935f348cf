-- Migration to populate section-based question structure
-- This migration updates existing questions to include sectionId and populates complete question data

-- First, run the sections migration if not already done
-- \i add_sections_support.sql

-- Function to update existing questions with section IDs
CREATE OR REPLACE FUNCTION update_questions_with_sections() RETURNS void AS $$
DECLARE
    config_record RECORD;
    updated_questions JSONB;
    question JSONB;
    question_key TEXT;
    section_id TEXT;
BEGIN
    -- Process each questionnaire configuration
    FOR config_record IN SELECT id, name, questions FROM questionnaire_configs WHERE questions IS NOT NULL LOOP
        updated_questions := '[]'::jsonb;

        -- Process each question in the questionnaire
        FOR question IN SELECT * FROM jsonb_array_elements(config_record.questions) LOOP
            question_key := question->>'key';
            section_id := NULL;

            -- Assign section IDs based on questionnaire type and question key using actual UUIDs
            IF config_record.name = 'THC Increase' THEN
                CASE
                    -- Step 1: Current Usage Patterns (3 questions)
                    WHEN question_key IN ('consistency', 'dosage', 'frequency') THEN
                        section_id := '0b8fc963-6a99-4c9e-bdda-270f2d6e8ce8'; -- Current Usage Patterns
                    -- Step 2: Treatment Assessment (2 questions + 1 conditional text)
                    WHEN question_key IN ('condition', 'conditionOther', 'effectiveness') THEN
                        section_id := '008d6157-a00b-4dc9-b870-e3f61bb7abc3'; -- Treatment Assessment
                    -- Step 3: Symptom Management (2 questions + 1 conditional text)
                    WHEN question_key IN ('symptomChanges', 'sideEffect', 'sideEffectsOther') THEN
                        section_id := '973d62d3-90a8-4320-8ead-6fe42d3070bd'; -- Symptom Management
                    -- Step 4: Side Effects Assessment (2 questions)
                    WHEN question_key IN ('sideEffectManageability', 'concerns') THEN
                        section_id := '01e4405a-d245-4477-b452-fd2409a7f09a'; -- Side Effects Assessment
                    -- Step 5: Treatment Effectiveness (2 questions)
                    WHEN question_key IN ('treatmentEffectiveness', 'weaknessAssessment') THEN
                        section_id := 'faf628b3-c503-44b0-b277-dff99daeaaba'; -- Treatment Effectiveness
                    -- Step 6: Relief and Satisfaction (2 questions)
                    WHEN question_key IN ('insufficientRelief', 'satisfactionWithForm') THEN
                        section_id := 'b22df526-5d47-460a-92d7-49e67a6d3cd5'; -- Relief and Satisfaction
                    -- Step 7: Future Treatment Preferences (2 questions)
                    WHEN question_key IN ('openToHigherPotency', 'quickReliefImportance') THEN
                        section_id := 'e4ab7200-84e6-4d7d-994f-554b3f935e31'; -- Future Treatment Preferences
                    -- Step 8: Treatment Continuation (2 questions)
                    WHEN question_key IN ('continueTreatment', 'overallSatisfaction') THEN
                        section_id := 'b8ea250d-d146-4191-8228-b0f7d018ae9e'; -- Treatment Continuation
                    ELSE
                        section_id := '0b8fc963-6a99-4c9e-bdda-270f2d6e8ce8'; -- Default to first section
                END CASE;

            ELSIF config_record.name = 'Extend Treatment Plan' THEN
                CASE
                    WHEN question_key IN ('adherence') THEN
                        section_id := '474aaa4f-ba55-443b-bd22-03f5976a154b'; -- Treatment Adherence
                    WHEN question_key IN ('symptomImprovement') THEN
                        section_id := '42ba45b9-842a-478a-aa08-f16776b3a58f'; -- Symptom Improvement
                    WHEN question_key IN ('symptomFrequency') THEN
                        section_id := 'bc0da3db-6b98-405c-bebe-32ae6fd09e42'; -- Symptom Frequency
                    WHEN question_key IN ('additionalRelief') THEN
                        section_id := '5f3239cd-896b-4002-833e-5f6bb403463c'; -- Additional Relief Needs
                    WHEN question_key IN ('functionalBenefit') THEN
                        section_id := '89c6400b-9ef2-46ef-b1d2-90e731c1196e'; -- Functional Benefits
                    WHEN question_key IN ('sleepQuality') THEN
                        section_id := 'd71a63a1-2c7d-42c6-afc3-6df62fb30d38'; -- Sleep Quality
                    WHEN question_key IN ('tolerance') THEN
                        section_id := '754cada4-bf71-4185-a056-6dd591cc6591'; -- Tolerance Development
                    WHEN question_key IN ('sideEffectSeverity') THEN
                        section_id := 'd0f1b120-2777-4a2d-9139-396ac5a1ad0c'; -- Side Effect Severity
                    WHEN question_key IN ('sideEffectTolerability') THEN
                        section_id := 'e006eed7-d9e5-4026-aa33-3c7bf196bcc5'; -- Side Effect Impact
                    WHEN question_key IN ('overallSatisfaction') THEN
                        section_id := '26206211-03e7-4cd2-8492-351ffa235ac6'; -- Overall Satisfaction
                    WHEN question_key IN ('goalAchievement') THEN
                        section_id := 'f3d9c6b2-76fa-472f-a84d-8f92a23e9922'; -- Goal Achievement
                    WHEN question_key IN ('treatmentIntent') THEN
                        section_id := 'b25fb8d9-47e4-4ca3-ba03-bcbf05ed6958'; -- Future Treatment Intent
                    ELSE
                        section_id := '474aaa4f-ba55-443b-bd22-03f5976a154b'; -- Default to first section
                END CASE;

            ELSIF config_record.name = 'Add 22% THC' THEN
                CASE
                    -- Step 1: Reasons for Request (5 checkboxes + 1 conditional text)
                    WHEN question_key IN ('reasonSideEffects', 'reasonGentlerEffect', 'reasonDifferentStrain', 'reasonTolerance', 'reasonOther', 'reasonOtherText') THEN
                        section_id := 'c4df331c-35bf-4ad0-a540-5aadd74aeee7'; -- Reasons for Request
                    -- Step 2: Current Treatment Response (1 slider + 4 checkboxes + 1 conditional text)
                    WHEN question_key IN ('symptomImprovement', 'sideEffectsNone', 'sideEffectsMild', 'sideEffectsModerate', 'sideEffectsStrong', 'sideEffectsDescription') THEN
                        section_id := '34d047c0-6ff0-4b32-9dcd-844cb56f2f7b'; -- Current Treatment Response
                    -- Step 3: Health Changes Assessment (1 radio + 1 conditional text)
                    WHEN question_key IN ('healthChanges', 'healthChangesDescription') THEN
                        section_id := '1e25a414-00ff-43f9-9c02-fa76342ab5ec'; -- Health Changes Assessment
                    -- Step 4: Expectations & Usage Planning (2 text + 1 radio)
                    WHEN question_key IN ('expectations', 'concerns', 'usagePlan') THEN
                        section_id := '266dd1bf-7d8b-42d0-989a-be2797d976a1'; -- Usage Planning
                    -- Step 5: Consent (1 radio)
                    WHEN question_key IN ('consent') THEN
                        section_id := '58606d86-ef83-4b9d-92f9-0bf19fd0eec1'; -- Consent
                    ELSE
                        section_id := 'c4df331c-35bf-4ad0-a540-5aadd74aeee7'; -- Default to first section
                END CASE;

            ELSIF config_record.name = 'Quantity Increase' THEN
                CASE
                    -- Step 1: Strength Selection & Reasons (8 questions: 2 checkboxes + 2 dropdowns + 5 checkboxes + 1 text)
                    WHEN question_key IN ('thc22Selected', 'thc29Selected', 'thc22RequestedQuantity', 'thc29RequestedQuantity', 'reasonNotLasting', 'reasonHigherDoses', 'reasonTolerance', 'reasonIncreasedSymptoms', 'reasonOther', 'reasonOtherText') THEN
                        section_id := 'a9e927d7-1b7a-44f7-9ad7-6bb417daf291'; -- Request Details & Reasons
                    -- Step 2: Treatment Response (1 slider + 4 checkboxes + 1 text + 1 radio + 1 text)
                    WHEN question_key IN ('currentEffectiveness', 'sideEffectsNone', 'sideEffectsMild', 'sideEffectsModerate', 'sideEffectsStrong', 'sideEffectsDescription', 'usageConsistency', 'usageConsistencyOther') THEN
                        section_id := '69f02b4a-7e24-47e2-b40c-160c92e89b27'; -- Treatment Response
                    -- Step 3: Health Changes (1 radio + 1 conditional text)
                    WHEN question_key IN ('healthChanges', 'healthChangesDescription') THEN
                        section_id := 'eeef76c1-e297-4dd4-b5fa-8169fd8a8aa5'; -- Health Changes
                    -- Step 4: Expectations & Preferences (2 text + 1 radio + 1 conditional text)
                    WHEN question_key IN ('expectations', 'concerns', 'intendedUsage', 'intendedUsageOther') THEN
                        section_id := 'ff4fc4d2-b60b-43c7-9a6c-70edf1b646f5'; -- Usage Planning
                    -- Step 5: Consent (1 radio)
                    WHEN question_key IN ('consent') THEN
                        section_id := '34e71031-e06b-41f8-b162-aa7f50f2e2c0'; -- Consent
                    ELSE
                        section_id := 'a9e927d7-1b7a-44f7-9ad7-6bb417daf291'; -- Default to first section
                END CASE;
            END IF;

            -- Add sectionId to the question if not already present
            IF section_id IS NOT NULL THEN
                question := question || jsonb_build_object('sectionId', section_id);
            END IF;

            -- Add the updated question to the array
            updated_questions := updated_questions || question;
        END LOOP;

        -- Update the questionnaire with the modified questions
        UPDATE questionnaire_configs
        SET questions = updated_questions
        WHERE id = config_record.id;

        RAISE NOTICE 'Updated questionnaire: % with % questions', config_record.name, jsonb_array_length(updated_questions);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to update existing questions
SELECT update_questions_with_sections();

-- Drop the function after use
DROP FUNCTION update_questions_with_sections();

-- Verify the updates
SELECT
    name,
    jsonb_array_length(questions) as question_count,
    (
        SELECT COUNT(*)
        FROM jsonb_array_elements(questions) as q
        WHERE q->>'sectionId' IS NOT NULL
    ) as questions_with_sections
FROM questionnaire_configs
WHERE questions IS NOT NULL;

COMMIT;
