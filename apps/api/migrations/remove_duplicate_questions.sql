-- Remove Duplicate Questions Migration
-- This migration removes duplicate questions that are causing incorrect counts

BEGIN;

-- Function to remove duplicate questions while keeping the first occurrence
CREATE OR REPLACE FUNCTION remove_duplicate_questions() RETURNS void AS $$
DECLARE
    config_record RECORD;
    cleaned_questions JSONB;
    question JSONB;
    seen_keys TEXT[];
    question_key TEXT;
BEGIN
    -- Process each questionnaire configuration
    FOR config_record IN SELECT id, name, questions FROM questionnaire_configs WHERE questions IS NOT NULL LOOP
        cleaned_questions := '[]'::jsonb;
        seen_keys := ARRAY[]::TEXT[];
        
        RAISE NOTICE 'Processing questionnaire: %', config_record.name;
        
        -- Process each question in the questionnaire
        FOR question IN SELECT * FROM jsonb_array_elements(config_record.questions) ORDER BY (value->>'order')::int LOOP
            question_key := question->>'key';
            
            -- Only add the question if we haven't seen this key before
            IF NOT (question_key = ANY(seen_keys)) THEN
                cleaned_questions := cleaned_questions || question;
                seen_keys := seen_keys || question_key;
                RAISE NOTICE '  Keeping question: %', question_key;
            ELSE
                RAISE NOTICE '  Removing duplicate: %', question_key;
            END IF;
        END LOOP;
        
        -- Update the questionnaire with cleaned questions
        UPDATE questionnaire_configs 
        SET questions = cleaned_questions,
            last_modified = NOW()
        WHERE id = config_record.id;
        
        RAISE NOTICE 'Updated % - Original: % questions, Cleaned: % questions', 
            config_record.name, 
            jsonb_array_length(config_record.questions),
            jsonb_array_length(cleaned_questions);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT remove_duplicate_questions();

-- Drop the function after use
DROP FUNCTION remove_duplicate_questions();

-- Verify the results
DO $$
DECLARE
    config_record RECORD;
BEGIN
    RAISE NOTICE '=== Final Question Counts ===';
    FOR config_record IN SELECT id, name, jsonb_array_length(questions) as question_count FROM questionnaire_configs ORDER BY name LOOP
        RAISE NOTICE '% - % questions', config_record.name, config_record.question_count;
    END LOOP;
END $$;

COMMIT;
