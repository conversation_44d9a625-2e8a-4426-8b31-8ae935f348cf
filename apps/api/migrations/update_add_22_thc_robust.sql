-- Robust migration for Add 22% THC questionnaire
-- Step 1: Update main config
UPDATE questionnaire_configs 
SET 
  name = 'Add 22% THC Option',
  max_score = 33,
  threshold = 7,
  sections_count = 5,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'add_22_thc';

-- Step 2: Clear existing sections
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

-- Step 3: Insert new sections with proper UUIDs
INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
('a1b2c3d4-e5f6-4890-abcd-ef1234567890', 'add_22_thc', 'Reasons for Request', 'Why do you want to add a 22% THC product to your treatment plan?', 1, '{"requireAllQuestions": false, "minimumQuestionsRequired": 1}', true),
('b2c3d4e5-f6a7-4901-bcde-f23456789012', 'add_22_thc', 'Current Treatment Response', 'How well has your current 29% THC treatment worked for you?', 2, '{"requireAllQuestions": true}', true),
('c3d4e5f6-a7b8-4012-cdef-************', 'add_22_thc', 'Health Changes', 'Any changes in your health, medications, or lifestyle since your last consultation?', 3, '{"requireAllQuestions": true}', true),
('d4e5f6a7-b8c9-4123-def0-************', 'add_22_thc', 'Expectations and Preferences', 'Your expectations and preferences for the 22% THC option', 4, '{"requireAllQuestions": true}', true),
('e5f6a7b8-c9d0-4234-ef01-************', 'add_22_thc', 'Consent', 'Consent for treatment plan review and update', 5, '{"requireAllQuestions": true}', true);

-- Step 4: Verify sections were created
SELECT 'Sections created:' as status, count(*) as section_count FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

-- Step 5: Update questions (this will be done in parts due to size)
-- First, let's verify current state
SELECT 'Current config:' as status, id, name, max_score, threshold, sections_count FROM questionnaire_configs WHERE id = 'add_22_thc';
