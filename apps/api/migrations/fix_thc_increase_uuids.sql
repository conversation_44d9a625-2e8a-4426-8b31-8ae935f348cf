-- Simple robust migration for THC Increase questionnaire with proper UUIDs
-- This replaces the complex multi-part approach with a single robust update

-- Step 1: Update main config
UPDATE questionnaire_configs 
SET 
  name = 'THC Increase (29% THC)',
  max_score = 61,
  threshold = 45,
  sections_count = 8,
  last_modified = NOW(),
  modified_by = 'system_migration'
WHERE id = 'thc_increase';

-- Step 2: Clear existing sections
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'thc_increase';

-- Step 3: Insert sections with proper UUIDs (using gen_random_uuid() for proper UUIDs)
INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
(gen_random_uuid(), 'thc_increase', 'Trial Usage Assessment', 'How consistent were you in using 22% THC flower during the two-week trial?', 1, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Condition and Treatment Effectiveness', 'What condition are you treating and how effective has it been?', 2, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Symptoms and Side Effects Assessment', 'Changes in symptoms and side effects experienced', 3, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Side Effect Management and Concerns', 'How manageable are side effects and any concerns', 4, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Treatment Effectiveness Assessment', 'Overall effectiveness and strength assessment', 5, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Relief and Treatment Satisfaction', 'Relief during episodes and satisfaction with form', 6, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Future Treatment Preferences', 'Openness to higher potency and importance of quick relief', 7, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Overall Treatment Experience', 'Likelihood to continue and overall satisfaction', 8, '{"requireAllQuestions": true}', true);

-- Step 4: Get the section IDs we just created (in order)
WITH section_ids AS (
  SELECT id, order_index 
  FROM questionnaire_sections 
  WHERE questionnaire_id = 'thc_increase' 
  ORDER BY order_index
),
section_mapping AS (
  SELECT 
    CASE order_index
      WHEN 1 THEN 'step1'
      WHEN 2 THEN 'step2'
      WHEN 3 THEN 'step3'
      WHEN 4 THEN 'step4'
      WHEN 5 THEN 'step5'
      WHEN 6 THEN 'step6'
      WHEN 7 THEN 'step7'
      WHEN 8 THEN 'step8'
    END as step_name,
    id as section_id
  FROM section_ids
)
SELECT 'Section mapping:' as info, step_name, section_id FROM section_mapping;

-- Step 5: For now, let's just verify the sections were created correctly
SELECT 'Sections created:' as status, count(*) as section_count 
FROM questionnaire_sections 
WHERE questionnaire_id = 'thc_increase';

-- Step 6: Show current config
SELECT 'Current config:' as status, id, name, max_score, threshold, sections_count 
FROM questionnaire_configs 
WHERE id = 'thc_increase';
