-- COMPREHENSIVE QUESTIONNAIRE FIX - ALL QUESTIONNAIRES TO EXACT SPECIFICATIONS
-- This migration ensures ALL questionnaires match their exact specification documents 100%

-- ========================================
-- 1. EXTEND TP QUESTIONNAIRE FIX
-- ========================================

-- Update main config
UPDATE questionnaire_configs 
SET 
  name = 'Extend Treatment Plan (6-Month Extension)',
  max_score = 60,
  threshold = 42,
  sections_count = 12,
  last_modified = NOW(),
  modified_by = 'system_migration_exact_spec'
WHERE id = 'extend_tp';

-- Update section descriptions to match specification
UPDATE questionnaire_sections 
SET description = 'During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 1;

UPDATE questionnaire_sections 
SET description = 'How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 2;

UPDATE questionnaire_sections 
SET description = 'How has the frequency or occurrence of your symptoms changed with treatment? (For example, how often you experience pain flare-ups, anxiety attacks, or sleepless nights now versus before.)'
WHERE questionnaire_id = 'extend_tp' AND order_index = 3;

UPDATE questionnaire_sections 
SET description = 'Did you need to use any extra treatments besides the prescribed cannabis to manage your condition (such as additional medications or extra cannabis doses)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 4;

UPDATE questionnaire_sections 
SET description = 'How has the treatment affected your daily functioning or quality of life (ability to perform work, household tasks, exercise, socialize, etc.)?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 5;

UPDATE questionnaire_sections 
SET description = 'How has your sleep quality or pattern been affected by the treatment?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 6;

UPDATE questionnaire_sections 
SET description = 'Did you find that you needed to increase your cannabis dose over time to get the same symptom relief?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 7;

UPDATE questionnaire_sections 
SET description = 'Which statement best describes the side effects you experienced from the medicinal cannabis?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 8;

UPDATE questionnaire_sections 
SET description = 'How did any side effects impact your willingness to continue treatment?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 9;

UPDATE questionnaire_sections 
SET description = 'Overall, how satisfied are you with the results of your medicinal cannabis treatment so far?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 10;

UPDATE questionnaire_sections 
SET description = 'To what extent has this treatment met the goals or expectations you had when you started?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 11;

UPDATE questionnaire_sections 
SET description = 'What would you like to do going forward after this 3-month trial?'
WHERE questionnaire_id = 'extend_tp' AND order_index = 12;

-- ========================================
-- 2. ADD 22% THC QUESTIONNAIRE FIX
-- ========================================

-- Update main config
UPDATE questionnaire_configs 
SET 
  name = 'Add 22% THC Option',
  max_score = 33,
  threshold = 7,
  sections_count = 5,
  last_modified = NOW(),
  modified_by = 'system_migration_exact_spec'
WHERE id = 'add_22_thc';

-- Clear existing sections and recreate with proper UUIDs
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'add_22_thc';

INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
(gen_random_uuid(), 'add_22_thc', 'Reasons for Request', 'Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)', 1, '{"requireAllQuestions": false, "minimumQuestionsRequired": 1}', true),
(gen_random_uuid(), 'add_22_thc', 'Current Treatment Response', 'How well has your current 29% THC treatment worked for you?', 2, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Health Changes', 'Any changes in your health, medications, or lifestyle since your last consultation?', 3, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Expectations and Preferences', 'Your expectations and preferences for the 22% THC option', 4, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'add_22_thc', 'Consent', 'Consent for treatment plan review and update', 5, '{"requireAllQuestions": true}', true);

-- ========================================
-- 3. THC INCREASE QUESTIONNAIRE FIX
-- ========================================

-- Update main config
UPDATE questionnaire_configs 
SET 
  name = 'THC Increase (29% THC)',
  max_score = 61,
  threshold = 45,
  sections_count = 8,
  last_modified = NOW(),
  modified_by = 'system_migration_exact_spec'
WHERE id = 'thc_increase';

-- Clear existing sections and recreate with proper UUIDs
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'thc_increase';

INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
(gen_random_uuid(), 'thc_increase', 'Trial Usage Assessment', 'How consistent were you in using 22% THC flower during the two-week trial?', 1, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Condition and Treatment Effectiveness', 'What condition are you treating and how effective has it been?', 2, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Symptoms and Side Effects Assessment', 'Changes in symptoms and side effects experienced', 3, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Side Effect Management and Concerns', 'How manageable are side effects and any concerns', 4, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Treatment Effectiveness Assessment', 'Overall effectiveness and strength assessment', 5, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Relief and Treatment Satisfaction', 'Relief during episodes and satisfaction with form', 6, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Future Treatment Preferences', 'Openness to higher potency and importance of quick relief', 7, '{"requireAllQuestions": true}', true),
(gen_random_uuid(), 'thc_increase', 'Overall Treatment Experience', 'Likelihood to continue and overall satisfaction', 8, '{"requireAllQuestions": true}', true);

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify all questionnaire configs
SELECT 'All questionnaire configs:' as status, id, name, max_score, threshold, sections_count 
FROM questionnaire_configs 
WHERE id IN ('extend_tp', 'add_22_thc', 'thc_increase')
ORDER BY id;

-- Verify all sections were created
SELECT 'Section counts:' as status, questionnaire_id, count(*) as section_count 
FROM questionnaire_sections 
WHERE questionnaire_id IN ('extend_tp', 'add_22_thc', 'thc_increase')
GROUP BY questionnaire_id
ORDER BY questionnaire_id;

-- Show completion status
SELECT 'Migration completed successfully - all questionnaires updated to exact specifications' as final_status;
