-- Update questionnaire configurations with complete questions based on revised documentation
-- This migration updates the existing questionnaire configs with all question types

-- Update THC Increase questionnaire with complete questions including all types
UPDATE questionnaire_configs 
SET 
  questions = '[
    {"key": "condition", "text": "What is your primary condition being treated?", "type": "special_logic", "order": 1, "contributesToScore": true, "answerOptions": [
      {"value": "chronic-pain", "label": "Chronic pain", "score": 2},
      {"value": "anxiety", "label": "Anxiety", "score": 2},
      {"value": "depression", "label": "Depression", "score": 2},
      {"value": "insomnia", "label": "Insomnia", "score": 2},
      {"value": "epilepsy", "label": "Epilepsy", "score": 2},
      {"value": "cancer", "label": "Cancer", "score": 2},
      {"value": "ptsd", "label": "PTSD", "score": 2},
      {"value": "other", "label": "Other", "score": 0}
    ], "specialLogic": {"description": "Any condition except other gets 2 points", "codeSnippet": "value === other ? 0 : 2"}},
    {"key": "conditionOther", "text": "Please describe your other condition", "type": "text", "order": 2, "contributesToScore": false, "textFieldConfig": {"maxLength": 500, "required": false, "placeholder": "Describe your condition...", "purpose": "Medical review for unlisted conditions"}},
    {"key": "consistency", "text": "How consistently do you use your current medication?", "type": "radio", "order": 3, "contributesToScore": true, "answerOptions": [
      {"value": "every-day", "label": "Every day", "score": 3},
      {"value": "most-days", "label": "Most days", "score": 2},
      {"value": "occasionally", "label": "Occasionally", "score": 1},
      {"value": "rarely", "label": "Rarely", "score": 0}
    ]},
    {"key": "dosage", "text": "What is your typical daily dosage?", "type": "radio", "order": 4, "contributesToScore": true, "answerOptions": [
      {"value": "less-than-0-5g", "label": "Less than 0.5g", "score": 1},
      {"value": "0-5g-1g", "label": "0.5g - 1g", "score": 2},
      {"value": "1g-2g", "label": "1g - 2g", "score": 3},
      {"value": "more-than-2g", "label": "More than 2g", "score": 4}
    ]},
    {"key": "frequency", "text": "How often do you use your medication per day?", "type": "radio", "order": 5, "contributesToScore": true, "answerOptions": [
      {"value": "once", "label": "Once daily", "score": 1},
      {"value": "twice", "label": "Twice daily", "score": 2},
      {"value": "three-times", "label": "Three times daily", "score": 3},
      {"value": "four-or-more", "label": "Four or more times daily", "score": 4}
    ]},
    {"key": "effectiveness", "text": "Rate the effectiveness of your current treatment (1-10)", "type": "slider", "order": 6, "contributesToScore": true, "sliderConfig": {"min": 1, "max": 10, "scoreMapping": {"1": 4, "2": 4, "3": 3, "4": 3, "5": 2, "6": 2, "7": 1, "8": 1, "9": 0, "10": 0}}},
    {"key": "sideEffect", "text": "What side effects are you currently experiencing?", "type": "special_logic", "order": 7, "contributesToScore": true, "answerOptions": [
      {"value": "none", "label": "None", "score": 4},
      {"value": "drowsiness", "label": "Drowsiness", "score": 1},
      {"value": "dry-mouth", "label": "Dry mouth", "score": 1},
      {"value": "dizziness", "label": "Dizziness", "score": 1},
      {"value": "anxiety", "label": "Anxiety", "score": 1},
      {"value": "other", "label": "Other", "score": 0}
    ], "specialLogic": {"description": "None = 4 points, known effects = 1 point, other = 0", "codeSnippet": "value === none ? 4 : (value === other ? 0 : 1)"}},
    {"key": "sideEffectsOther", "text": "Please describe other side effects", "type": "text", "order": 8, "contributesToScore": false, "textFieldConfig": {"maxLength": 300, "required": false, "placeholder": "Describe side effects...", "purpose": "Medical review for unlisted side effects"}},
    {"key": "sideEffectManageability", "text": "How manageable are your current side effects? (1-10)", "type": "slider", "order": 9, "contributesToScore": true, "sliderConfig": {"min": 1, "max": 10, "scoreMapping": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 2, "6": 2, "7": 3, "8": 3, "9": 4, "10": 4}}},
    {"key": "symptomChanges", "text": "How have your symptoms changed recently?", "type": "radio", "order": 10, "contributesToScore": true, "answerOptions": [
      {"value": "much-worse", "label": "Much worse", "score": 4},
      {"value": "worse", "label": "Worse", "score": 3},
      {"value": "same", "label": "About the same", "score": 2},
      {"value": "better", "label": "Better", "score": 1},
      {"value": "much-better", "label": "Much better", "score": 0}
    ]},
    {"key": "concerns", "text": "Do you have concerns about increasing THC?", "type": "radio", "order": 11, "contributesToScore": true, "answerOptions": [
      {"value": "no-concerns", "label": "No concerns", "score": 3},
      {"value": "minor-concerns", "label": "Minor concerns", "score": 2},
      {"value": "moderate-concerns", "label": "Moderate concerns", "score": 1},
      {"value": "major-concerns", "label": "Major concerns", "score": 0}
    ]},
    {"key": "treatmentEffectiveness", "text": "Overall treatment effectiveness", "type": "radio", "order": 12, "contributesToScore": true, "answerOptions": [
      {"value": "very-effective", "label": "Very effective", "score": 1},
      {"value": "effective", "label": "Effective", "score": 2},
      {"value": "somewhat-effective", "label": "Somewhat effective", "score": 3},
      {"value": "not-effective", "label": "Not effective", "score": 4}
    ]},
    {"key": "weaknessAssessment", "text": "Is your current treatment too weak?", "type": "radio", "order": 13, "contributesToScore": true, "answerOptions": [
      {"value": "definitely-too-weak", "label": "Definitely too weak", "score": 4},
      {"value": "probably-too-weak", "label": "Probably too weak", "score": 3},
      {"value": "appropriate-strength", "label": "Appropriate strength", "score": 1},
      {"value": "too-strong", "label": "Too strong", "score": 0}
    ]},
    {"key": "insufficientRelief", "text": "Do you experience insufficient relief?", "type": "radio", "order": 14, "contributesToScore": true, "answerOptions": [
      {"value": "always", "label": "Always", "score": 4},
      {"value": "often", "label": "Often", "score": 3},
      {"value": "sometimes", "label": "Sometimes", "score": 2},
      {"value": "rarely", "label": "Rarely", "score": 1},
      {"value": "never", "label": "Never", "score": 0}
    ]},
    {"key": "satisfactionWithForm", "text": "Satisfaction with current form", "type": "radio", "order": 15, "contributesToScore": true, "answerOptions": [
      {"value": "very-satisfied", "label": "Very satisfied", "score": 0},
      {"value": "satisfied", "label": "Satisfied", "score": 1},
      {"value": "neutral", "label": "Neutral", "score": 2},
      {"value": "dissatisfied", "label": "Dissatisfied", "score": 3},
      {"value": "very-dissatisfied", "label": "Very dissatisfied", "score": 4}
    ]},
    {"key": "openToHigherPotency", "text": "Are you open to higher potency?", "type": "radio", "order": 16, "contributesToScore": true, "answerOptions": [
      {"value": "very-open", "label": "Very open", "score": 4},
      {"value": "open", "label": "Open", "score": 3},
      {"value": "neutral", "label": "Neutral", "score": 2},
      {"value": "hesitant", "label": "Hesitant", "score": 1},
      {"value": "not-open", "label": "Not open", "score": 0}
    ]},
    {"key": "quickReliefImportance", "text": "Importance of quick relief", "type": "radio", "order": 17, "contributesToScore": true, "answerOptions": [
      {"value": "very-important", "label": "Very important", "score": 4},
      {"value": "important", "label": "Important", "score": 3},
      {"value": "somewhat-important", "label": "Somewhat important", "score": 2},
      {"value": "not-important", "label": "Not important", "score": 1}
    ]},
    {"key": "continueTreatment", "text": "Likelihood to continue treatment", "type": "radio", "order": 18, "contributesToScore": true, "answerOptions": [
      {"value": "very-likely", "label": "Very likely", "score": 3},
      {"value": "likely", "label": "Likely", "score": 2},
      {"value": "unlikely", "label": "Unlikely", "score": 1},
      {"value": "very-unlikely", "label": "Very unlikely", "score": 0}
    ]},
    {"key": "overallSatisfaction", "text": "Overall satisfaction", "type": "radio", "order": 19, "contributesToScore": true, "answerOptions": [
      {"value": "very-satisfied", "label": "Very satisfied", "score": 0},
      {"value": "satisfied", "label": "Satisfied", "score": 1},
      {"value": "neutral", "label": "Neutral", "score": 2},
      {"value": "dissatisfied", "label": "Dissatisfied", "score": 3},
      {"value": "very-dissatisfied", "label": "Very dissatisfied", "score": 4}
    ]}
  ]'::jsonb,
  max_score = 61,
  threshold = 45,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'thc_increase';

-- Update ExtendTP questionnaire with complete questions
UPDATE questionnaire_configs
SET
  questions = '[
    {"key": "adherence", "text": "Treatment plan adherence", "type": "radio", "order": 1, "contributesToScore": true, "answerOptions": [
      {"value": "always-followed", "label": "Always followed", "score": 5},
      {"value": "usually-followed", "label": "Usually followed", "score": 4},
      {"value": "sometimes-followed", "label": "Sometimes followed", "score": 2},
      {"value": "rarely-followed", "label": "Rarely followed", "score": 1},
      {"value": "never-followed", "label": "Never followed", "score": 0}
    ]},
    {"key": "symptomImprovement", "text": "Symptom improvement", "type": "radio", "order": 2, "contributesToScore": true, "answerOptions": [
      {"value": "significant-improvement", "label": "Significant improvement", "score": 4},
      {"value": "moderate-improvement", "label": "Moderate improvement", "score": 3},
      {"value": "slight-improvement", "label": "Slight improvement", "score": 2},
      {"value": "no-improvement", "label": "No improvement", "score": 1},
      {"value": "worsened", "label": "Symptoms worsened", "score": 0}
    ]},
    {"key": "symptomFrequency", "text": "Symptom frequency changes", "type": "radio", "order": 3, "contributesToScore": true, "answerOptions": [
      {"value": "much-less-frequent", "label": "Much less frequent", "score": 4},
      {"value": "less-frequent", "label": "Less frequent", "score": 3},
      {"value": "same-frequency", "label": "Same frequency", "score": 2},
      {"value": "more-frequent", "label": "More frequent", "score": 1},
      {"value": "much-more-frequent", "label": "Much more frequent", "score": 0}
    ]},
    {"key": "additionalRelief", "text": "Need for additional relief", "type": "radio", "order": 4, "contributesToScore": true, "answerOptions": [
      {"value": "no-additional-needed", "label": "No additional relief needed", "score": 4},
      {"value": "minimal-additional", "label": "Minimal additional relief needed", "score": 3},
      {"value": "moderate-additional", "label": "Moderate additional relief needed", "score": 2},
      {"value": "significant-additional", "label": "Significant additional relief needed", "score": 1},
      {"value": "complete-change-needed", "label": "Complete treatment change needed", "score": 0}
    ]},
    {"key": "functionalBenefit", "text": "Functional improvement", "type": "radio", "order": 5, "contributesToScore": true, "answerOptions": [
      {"value": "major-improvement", "label": "Major functional improvement", "score": 4},
      {"value": "moderate-improvement", "label": "Moderate improvement", "score": 3},
      {"value": "slight-improvement", "label": "Slight improvement", "score": 2},
      {"value": "no-change", "label": "No functional change", "score": 1},
      {"value": "decline", "label": "Functional decline", "score": 0}
    ]},
    {"key": "sleepQuality", "text": "Sleep quality changes", "type": "radio", "order": 6, "contributesToScore": true, "answerOptions": [
      {"value": "much-better", "label": "Much better sleep", "score": 4},
      {"value": "better", "label": "Better sleep", "score": 3},
      {"value": "same", "label": "Same sleep quality", "score": 2},
      {"value": "worse", "label": "Worse sleep", "score": 1},
      {"value": "much-worse", "label": "Much worse sleep", "score": 0}
    ]},
    {"key": "tolerance", "text": "Tolerance development", "type": "radio", "order": 7, "contributesToScore": true, "answerOptions": [
      {"value": "no-tolerance", "label": "No tolerance developed", "score": 4},
      {"value": "minimal-tolerance", "label": "Minimal tolerance", "score": 3},
      {"value": "moderate-tolerance", "label": "Moderate tolerance", "score": 2},
      {"value": "significant-tolerance", "label": "Significant tolerance", "score": 1},
      {"value": "high-tolerance", "label": "High tolerance", "score": 0}
    ]},
    {"key": "sideEffectSeverity", "text": "Side effect severity", "type": "radio", "order": 8, "contributesToScore": true, "answerOptions": [
      {"value": "none", "label": "No side effects", "score": 4},
      {"value": "mild", "label": "Mild side effects", "score": 3},
      {"value": "moderate", "label": "Moderate side effects", "score": 2},
      {"value": "severe", "label": "Severe side effects", "score": 1},
      {"value": "intolerable", "label": "Intolerable side effects", "score": 0}
    ]},
    {"key": "sideEffectTolerability", "text": "Side effect impact", "type": "radio", "order": 9, "contributesToScore": true, "answerOptions": [
      {"value": "no-impact", "label": "No impact on daily life", "score": 4},
      {"value": "minimal-impact", "label": "Minimal impact", "score": 3},
      {"value": "moderate-impact", "label": "Moderate impact", "score": 2},
      {"value": "significant-impact", "label": "Significant impact", "score": 1},
      {"value": "severe-impact", "label": "Severe impact on daily life", "score": 0}
    ]},
    {"key": "overallSatisfaction", "text": "Overall satisfaction", "type": "radio", "order": 10, "contributesToScore": true, "answerOptions": [
      {"value": "very-satisfied", "label": "Very satisfied", "score": 4},
      {"value": "satisfied", "label": "Satisfied", "score": 3},
      {"value": "neutral", "label": "Neutral", "score": 2},
      {"value": "dissatisfied", "label": "Dissatisfied", "score": 1},
      {"value": "very-dissatisfied", "label": "Very dissatisfied", "score": 0}
    ]},
    {"key": "goalAchievement", "text": "Treatment goal achievement", "type": "radio", "order": 11, "contributesToScore": true, "answerOptions": [
      {"value": "exceeded-goals", "label": "Exceeded treatment goals", "score": 4},
      {"value": "met-goals", "label": "Met treatment goals", "score": 3},
      {"value": "partially-met", "label": "Partially met goals", "score": 2},
      {"value": "minimal-progress", "label": "Minimal progress toward goals", "score": 1},
      {"value": "no-progress", "label": "No progress toward goals", "score": 0}
    ]},
    {"key": "treatmentIntent", "text": "Future treatment intention", "type": "radio", "order": 12, "contributesToScore": true, "answerOptions": [
      {"value": "definitely-continue", "label": "Definitely want to continue", "score": 4},
      {"value": "likely-continue", "label": "Likely to continue", "score": 3},
      {"value": "unsure", "label": "Unsure about continuing", "score": 2},
      {"value": "unlikely-continue", "label": "Unlikely to continue", "score": 1},
      {"value": "definitely-stop", "label": "Definitely want to stop", "score": 0}
    ]}
  ]'::jsonb,
  max_score = 48,
  threshold = 35,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'extend_tp';

-- Update Add 22% THC questionnaire with complete questions including all types
UPDATE questionnaire_configs
SET
  questions = '[
    {"key": "reasonSideEffects", "text": "Experiencing side effects with 29%", "type": "checkbox", "order": 1, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonGentlerEffect", "text": "Want gentler effect", "type": "checkbox", "order": 2, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonDifferentStrain", "text": "Want different strain option", "type": "checkbox", "order": 3, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonTolerance", "text": "Tolerance to current strength", "type": "checkbox", "order": 4, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonOther", "text": "Other reason", "type": "checkbox", "order": 5, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 1},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonOtherText", "text": "Please describe other reason", "type": "text", "order": 6, "contributesToScore": false, "textFieldConfig": {"maxLength": 300, "required": false, "placeholder": "Describe your reason...", "purpose": "Medical review for unlisted reasons"}},
    {"key": "sideEffectsNone", "text": "No side effects", "type": "checkbox", "order": 7, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "sideEffectsMild", "text": "Mild side effects", "type": "checkbox", "order": 8, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 2},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "sideEffectsModerate", "text": "Moderate side effects", "type": "checkbox", "order": 9, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 1},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "sideEffectsStrong", "text": "Strong side effects", "type": "checkbox", "order": 10, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 0},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "sideEffectsDescription", "text": "Description of side effects", "type": "text", "order": 11, "contributesToScore": false, "textFieldConfig": {"maxLength": 500, "required": false, "placeholder": "Describe your side effects...", "purpose": "Medical review of side effect details"}},
    {"key": "symptomImprovement", "text": "Symptom improvement rating (1-10 scale)", "type": "slider", "order": 12, "contributesToScore": true, "sliderConfig": {"min": 1, "max": 10, "scoreMapping": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 2, "6": 2, "7": 3, "8": 3, "9": 4, "10": 4}}},
    {"key": "healthChanges", "text": "Recent health changes", "type": "radio", "order": 13, "contributesToScore": true, "answerOptions": [
      {"value": "improved", "label": "Health improved", "score": 3},
      {"value": "stable", "label": "Health stable", "score": 2},
      {"value": "declined", "label": "Health declined", "score": 1},
      {"value": "significantly-declined", "label": "Health significantly declined", "score": 0}
    ]},
    {"key": "healthChangesDescription", "text": "Description of health changes", "type": "text", "order": 14, "contributesToScore": false, "textFieldConfig": {"maxLength": 400, "required": false, "placeholder": "Describe health changes...", "purpose": "Medical review of health status changes"}},
    {"key": "usagePlan", "text": "How would you use 22% THC?", "type": "radio", "order": 15, "contributesToScore": true, "answerOptions": [
      {"value": "replace-29", "label": "Replace 29% completely", "score": 3},
      {"value": "supplement-29", "label": "Use alongside 29%", "score": 2},
      {"value": "trial-period", "label": "Trial period first", "score": 2},
      {"value": "specific-situations", "label": "For specific situations", "score": 1}
    ]},
    {"key": "expectations", "text": "Patient expectations", "type": "text", "order": 16, "contributesToScore": false, "textFieldConfig": {"maxLength": 400, "required": false, "placeholder": "What do you expect from 22% THC?", "purpose": "Understanding patient expectations for treatment planning"}},
    {"key": "concerns", "text": "Patient concerns", "type": "text", "order": 17, "contributesToScore": false, "textFieldConfig": {"maxLength": 400, "required": false, "placeholder": "Any concerns about adding 22% THC?", "purpose": "Addressing patient concerns before treatment"}},
    {"key": "consent", "text": "Consent to add 22% THC", "type": "radio", "order": 18, "contributesToScore": true, "answerOptions": [
      {"value": "full-consent", "label": "Full consent with understanding", "score": 4},
      {"value": "consent-with-questions", "label": "Consent but have questions", "score": 3},
      {"value": "hesitant-consent", "label": "Hesitant but willing to try", "score": 2},
      {"value": "no-consent", "label": "Do not consent", "score": 0}
    ]}
  ]'::jsonb,
  max_score = 45,
  threshold = 15,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'add_22_thc';

-- Update Quantity Increase questionnaire with complete questions including all types
UPDATE questionnaire_configs
SET
  questions = '[
    {"key": "usageConsistency", "text": "Usage consistency", "type": "radio", "order": 1, "contributesToScore": true, "answerOptions": [
      {"value": "very-consistent", "label": "Very consistent daily use", "score": 4},
      {"value": "mostly-consistent", "label": "Mostly consistent", "score": 3},
      {"value": "somewhat-consistent", "label": "Somewhat consistent", "score": 2},
      {"value": "inconsistent", "label": "Inconsistent use", "score": 1}
    ]},
    {"key": "reasonNotLasting", "text": "Current amount not lasting", "type": "checkbox", "order": 2, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonHigherDoses", "text": "Need higher doses for effect", "type": "checkbox", "order": 3, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonTolerance", "text": "Developed tolerance", "type": "checkbox", "order": 4, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 3},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonIncreasedSymptoms", "text": "Symptoms have increased", "type": "checkbox", "order": 5, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 4},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "reasonOther", "text": "Other reason", "type": "checkbox", "order": 6, "contributesToScore": true, "answerOptions": [
      {"value": "true", "label": "Yes", "score": 1},
      {"value": "false", "label": "No", "score": 0}
    ]},
    {"key": "currentEffectiveness", "text": "Current treatment effectiveness (1-10 scale)", "type": "slider", "order": 7, "contributesToScore": true, "sliderConfig": {"min": 1, "max": 10, "scoreMapping": {"1": 4, "2": 4, "3": 3, "4": 3, "5": 2, "6": 2, "7": 1, "8": 1, "9": 0, "10": 0}}},
    {"key": "healthChanges", "text": "Health status changes", "type": "radio", "order": 8, "contributesToScore": true, "answerOptions": [
      {"value": "significantly-worse", "label": "Significantly worse", "score": 4},
      {"value": "worse", "label": "Worse", "score": 3},
      {"value": "stable", "label": "Stable", "score": 2},
      {"value": "improved", "label": "Improved", "score": 1},
      {"value": "significantly-improved", "label": "Significantly improved", "score": 0}
    ]},
    {"key": "intendedUsage", "text": "How increased quantity will be used", "type": "radio", "order": 9, "contributesToScore": true, "answerOptions": [
      {"value": "same-frequency-higher-dose", "label": "Same frequency, higher doses", "score": 3},
      {"value": "more-frequent-same-dose", "label": "More frequent, same dose", "score": 2},
      {"value": "combination-approach", "label": "Combination of both", "score": 4},
      {"value": "emergency-reserve", "label": "Keep extra as reserve", "score": 1}
    ]},
    {"key": "expectations", "text": "Patient expectations", "type": "text", "order": 10, "contributesToScore": false, "textFieldConfig": {"maxLength": 400, "required": false, "placeholder": "What do you expect from increased quantity?", "purpose": "Understanding patient expectations for treatment planning"}},
    {"key": "concerns", "text": "Patient concerns", "type": "text", "order": 11, "contributesToScore": false, "textFieldConfig": {"maxLength": 400, "required": false, "placeholder": "Any concerns about quantity increase?", "purpose": "Addressing patient concerns before treatment"}},
    {"key": "consent", "text": "Consent to quantity increase", "type": "radio", "order": 12, "contributesToScore": true, "answerOptions": [
      {"value": "full-consent", "label": "Full consent with understanding", "score": 4},
      {"value": "consent-with-questions", "label": "Consent but have questions", "score": 3},
      {"value": "hesitant-consent", "label": "Hesitant but willing to try", "score": 2},
      {"value": "no-consent", "label": "Do not consent", "score": 0}
    ]}
  ]'::jsonb,
  max_score = 40,
  threshold = 25,
  last_modified = NOW(),
  modified_by = 'system'
WHERE id = 'quantity_increase';
