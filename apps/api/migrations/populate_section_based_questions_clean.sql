-- Migration to populate section-based question structure
-- This migration updates existing questions to include sectionId and populates complete question data

-- First, run the sections migration if not already done
-- \i add_sections_support.sql

-- Function to update existing questions with section IDs
CREATE OR REPLACE FUNCTION update_questions_with_sections() RETURNS void AS $$
DECLARE
    config_record RECORD;
    updated_questions JSONB;
    question JSONB;
    question_key TEXT;
    section_id TEXT;
BEGIN
    -- Process each questionnaire configuration
    FOR config_record IN SELECT id, name, questions FROM questionnaire_configs WHERE questions IS NOT NULL LOOP
        updated_questions := '[]'::jsonb;
        
        -- Process each question in the questionnaire
        FOR question IN SELECT * FROM jsonb_array_elements(config_record.questions) LOOP
            question_key := question->>'key';
            section_id := NULL;
            
            -- Assign section IDs based on questionnaire type and question key
            IF config_record.name = 'THC_INCREASE' THEN
                CASE 
                    WHEN question_key IN ('consistency', 'dosage', 'frequency', 'effectiveness', 'side_effects') THEN
                        section_id := 'thc_increase_section_1';
                    WHEN question_key IN ('pain_level', 'sleep_quality', 'anxiety_level', 'appetite') THEN
                        section_id := 'thc_increase_section_2';
                    WHEN question_key IN ('tolerance', 'previous_increases', 'medical_conditions') THEN
                        section_id := 'thc_increase_section_3';
                    WHEN question_key IN ('lifestyle_impact', 'work_impact', 'social_impact') THEN
                        section_id := 'thc_increase_section_4';
                    WHEN question_key IN ('doctor_consultation', 'medication_interactions', 'allergies') THEN
                        section_id := 'thc_increase_section_5';
                    WHEN question_key IN ('goals', 'expectations', 'timeline') THEN
                        section_id := 'thc_increase_section_6';
                    WHEN question_key IN ('support_system', 'monitoring', 'follow_up') THEN
                        section_id := 'thc_increase_section_7';
                    WHEN question_key IN ('medical_history', 'current_medications', 'health_status') THEN
                        section_id := 'thc_increase_section_8';
                    ELSE
                        section_id := 'thc_increase_section_1'; -- Default to first section
                END CASE;
                
            ELSIF config_record.name = 'EXTEND_TP' THEN
                CASE 
                    WHEN question_key IN ('current_effectiveness', 'symptom_relief', 'dosage_adequacy') THEN
                        section_id := 'extend_tp_section_1';
                    WHEN question_key IN ('side_effects', 'tolerance', 'dependency_concerns') THEN
                        section_id := 'extend_tp_section_2';
                    WHEN question_key IN ('lifestyle_improvement', 'quality_of_life', 'daily_functioning') THEN
                        section_id := 'extend_tp_section_3';
                    WHEN question_key IN ('compliance', 'missed_doses', 'consistency') THEN
                        section_id := 'extend_tp_section_4';
                    WHEN question_key IN ('alternative_treatments', 'other_medications', 'therapy') THEN
                        section_id := 'extend_tp_section_5';
                    WHEN question_key IN ('doctor_visits', 'monitoring', 'lab_results') THEN
                        section_id := 'extend_tp_section_6';
                    WHEN question_key IN ('financial_impact', 'insurance_coverage', 'cost_concerns') THEN
                        section_id := 'extend_tp_section_7';
                    WHEN question_key IN ('family_support', 'social_impact', 'relationships') THEN
                        section_id := 'extend_tp_section_8';
                    WHEN question_key IN ('work_productivity', 'career_impact', 'disability') THEN
                        section_id := 'extend_tp_section_9';
                    WHEN question_key IN ('future_goals', 'treatment_expectations', 'long_term_plans') THEN
                        section_id := 'extend_tp_section_10';
                    WHEN question_key IN ('emergency_situations', 'crisis_management', 'backup_plans') THEN
                        section_id := 'extend_tp_section_11';
                    WHEN question_key IN ('overall_satisfaction', 'recommendation', 'feedback') THEN
                        section_id := 'extend_tp_section_12';
                    ELSE
                        section_id := 'extend_tp_section_1'; -- Default to first section
                END CASE;
                
            ELSIF config_record.name = 'ADD_22_THC' THEN
                CASE 
                    WHEN question_key IN ('current_treatment', 'current_effectiveness', 'current_dosage') THEN
                        section_id := 'add_22_section_1';
                    WHEN question_key IN ('symptom_severity', 'pain_levels', 'breakthrough_symptoms') THEN
                        section_id := 'add_22_section_2';
                    WHEN question_key IN ('previous_experience', 'thc_tolerance', 'cannabis_history') THEN
                        section_id := 'add_22_section_3';
                    WHEN question_key IN ('lifestyle_factors', 'work_schedule', 'daily_activities') THEN
                        section_id := 'add_22_section_4';
                    WHEN question_key IN ('medical_considerations', 'contraindications', 'drug_interactions') THEN
                        section_id := 'add_22_section_5';
                    ELSE
                        section_id := 'add_22_section_1'; -- Default to first section
                END CASE;
                
            ELSIF config_record.name = 'QUANTITY_INCREASE' THEN
                CASE 
                    WHEN question_key IN ('usage_assessment', 'current_quantity', 'consumption_rate') THEN
                        section_id := 'quantity_increase_section_1';
                    WHEN question_key IN ('symptom_management', 'relief_duration', 'breakthrough_symptoms') THEN
                        section_id := 'quantity_increase_section_2';
                    WHEN question_key IN ('tolerance_development', 'effectiveness_decline', 'dose_escalation') THEN
                        section_id := 'quantity_increase_section_3';
                    WHEN question_key IN ('lifestyle_impact', 'daily_functioning', 'quality_of_life') THEN
                        section_id := 'quantity_increase_section_4';
                    WHEN question_key IN ('treatment_optimization', 'dosing_schedule', 'administration_method') THEN
                        section_id := 'quantity_increase_section_5';
                    ELSE
                        section_id := 'quantity_increase_section_1'; -- Default to first section
                END CASE;
            END IF;
            
            -- Add sectionId to the question if not already present
            IF section_id IS NOT NULL THEN
                question := question || jsonb_build_object('sectionId', section_id);
            END IF;
            
            -- Add the updated question to the array
            updated_questions := updated_questions || question;
        END LOOP;
        
        -- Update the questionnaire with the modified questions
        UPDATE questionnaire_configs 
        SET questions = updated_questions 
        WHERE id = config_record.id;
        
        RAISE NOTICE 'Updated questionnaire: % with % questions', config_record.name, jsonb_array_length(updated_questions);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to update existing questions
SELECT update_questions_with_sections();

-- Drop the function after use
DROP FUNCTION update_questions_with_sections();

-- Verify the updates
SELECT 
    name,
    jsonb_array_length(questions) as question_count,
    (
        SELECT COUNT(*)
        FROM jsonb_array_elements(questions) as q
        WHERE q->>'sectionId' IS NOT NULL
    ) as questions_with_sections
FROM questionnaire_configs 
WHERE questions IS NOT NULL;

COMMIT;
