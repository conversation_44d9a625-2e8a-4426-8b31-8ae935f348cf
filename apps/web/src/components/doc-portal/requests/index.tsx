import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Box, Typography, Chip, CircularProgress, IconButton, <PERSON>ton, Card, Accordion, AccordionSummary, AccordionDetails, Stack, Dialog, Alert } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useSnackbar } from 'notistack';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import { ApiClient } from '../../../services';
import RequestCard from './RequestCard';
import PatientChatView from './PatientChatView';
import { usePatient } from '../../../hooks/patient-provider';
import { useAuth } from '../../../hooks/auth-provider';
import Order from '../patientHistory/orders/order';
import QuestionnaireForm from '../patientHistory/questionnaire';
import TreatmentPlan from '../patientHistory/treatmentPlan';
import HealthCheck from '../patientHistory/healthCheck';
import DoctorChatNotifications from '../DoctorChatNotifications';

import { provinceAbbreviation, safeScriptLinksPerProvince, convertTimeStampToReadableFormat } from '../../../utils';

interface Request {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  patient_id: string;
  patient_name: string;
  patient_dob?: string;
  email: string;
  questionnaire_data: any[];
  total_score: number;
  max_score: number;
  is_eligible: boolean;
  status: string;
  created_at: string;
  approved_at?: string;
  approved_by?: string;
  review_notes?: string;
  strength_requests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
}

interface RequestCounts {
  all: number;
  requests: number;
  unread: number;
}

// Helper functions for SafeScript link
const getSafeScriptLink = (province: string | undefined): string | undefined => {
  if (!province) return undefined;
  const smallCase = province.toLowerCase();
  return safeScriptLinksPerProvince[smallCase] ?? undefined;
};

const getProvinceAbbreviation = (province: string | undefined): string | undefined => {
  if (!province) return undefined;
  const smallCase = province.toLowerCase();
  return provinceAbbreviation[smallCase] ?? undefined;
};

type FilterType = 'all' | 'requests' | 'unread';

const RequestsWindow: React.FC = () => {
  const [allRequests, setAllRequests] = useState<Request[]>([]);
  const [counts, setCounts] = useState<RequestCounts>({ all: 0, requests: 0, unread: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<{
    patientId: string;
    patientName: string;
    channelId?: string;
    patientEmail?: string;
    patientState?: string;
    request?: Request;
  } | null>(null);
  const [patientData, setPatientData] = useState<any>(null);
  const [expandedRequest, setExpandedRequest] = useState<Request | null>(null);
  const [patientHistory, setPatientHistory] = useState<any[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState<FilterType>('requests');
  const { enqueueSnackbar } = useSnackbar();
  const { doctor } = useAuth();

  // Patient history dialog functionality
  const {
    isPatientHistoryDialogOpen,
    isPatientHistoryLoading,
    patientHistoryError,
    openPatientHistoryDialog,
    closePatientHistoryDialog,
    selectedPatientHistory,
    setSelectedPatientHistory,
    patientHistory: contextPatientHistory
  } = usePatient();

  // Copy functions for patient information
  const copyToClipboard = useCallback((text: string | undefined, infoType: string) => {
    if (!text) {
      enqueueSnackbar(`No ${infoType} available to copy`, {
        variant: 'error',
        persist: true
      });
      return;
    }

    // Check if clipboard API is available
    if (!navigator.clipboard) {
      enqueueSnackbar('Clipboard API not available in your browser', {
        variant: 'error',
        persist: true
      });
      return;
    }

    navigator.clipboard.writeText(text)
      .then(() => {
        enqueueSnackbar(`${infoType} copied to clipboard`, {
          variant: 'success',
          autoHideDuration: 4000
        });
      })
      .catch(err => {
        enqueueSnackbar(`Failed to copy ${infoType}: ${err}`, {
          variant: 'error',
          persist: true
        });
      });
  }, [enqueueSnackbar]);

  const copyFirstName = useCallback(() => {
    // Use patient name from request data
    if (expandedRequest?.patient_name) {
      const nameParts = expandedRequest.patient_name.trim().split(' ');
      if (nameParts.length > 0) {
        copyToClipboard(nameParts[0], 'First Name');
        return;
      }
    }

    // Fallback to patientData if available
    if (patientData?.firstName) {
      copyToClipboard(patientData.firstName, 'First Name');
      return;
    }

    if (patientData?.fullName) {
      const nameParts = patientData.fullName.trim().split(' ');
      if (nameParts.length > 0) {
        copyToClipboard(nameParts[0], 'First Name');
        return;
      }
    }

    enqueueSnackbar('No first name available to copy', { variant: 'error' });
  }, [expandedRequest, patientData, copyToClipboard, enqueueSnackbar]);

  const copyLastName = useCallback(() => {
    // Use patient name from request data
    if (expandedRequest?.patient_name) {
      const nameParts = expandedRequest.patient_name.trim().split(' ');
      if (nameParts.length > 1) {
        copyToClipboard(nameParts[nameParts.length - 1], 'Last Name');
        return;
      }
    }

    // Fallback to patientData if available
    if (patientData?.lastName) {
      copyToClipboard(patientData.lastName, 'Last Name');
      return;
    }

    if (patientData?.fullName) {
      const nameParts = patientData.fullName.trim().split(' ');
      if (nameParts.length > 1) {
        copyToClipboard(nameParts[nameParts.length - 1], 'Last Name');
        return;
      }
    }

    enqueueSnackbar('No last name available to copy', { variant: 'error' });
  }, [expandedRequest, patientData, copyToClipboard, enqueueSnackbar]);

  const copyDOB = useCallback(() => {
    // Use patient DOB from request data first
    if (expandedRequest?.patient_dob) {
      copyToClipboard(expandedRequest.patient_dob, 'Date of Birth');
      return;
    }

    // Fallback to patientData if available
    copyToClipboard(patientData?.dob, 'Date of Birth');
  }, [expandedRequest, patientData, copyToClipboard]);

  const fetchRequests = async (includeAll: boolean = false) => {
    try {
      setLoading(true);
      const response = await ApiClient.getDoctorPendingRequests(includeAll);
      const updatedRequests = response.data.requests || [];
      setAllRequests(updatedRequests);
      setCounts(response.data.counts || { all: 0, requests: 0, unread: 0 });
      setError(null);
      return updatedRequests;
    } catch (err) {
      console.error('Error fetching requests:', err);
      setError('Failed to load requests');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Computed filtered requests based on active filter
  const filteredRequests = useMemo(() => {
    switch (activeFilter) {
      case 'all':
        return allRequests; // Will include all statuses when fetched with includeAll=true
      case 'requests':
        return allRequests.filter(request =>
          request.status === 'pending' || request.status === 'submitted'
        );
      case 'unread':
        return allRequests.filter(request => request.status === 'pending');
      default:
        return allRequests;
    }
  }, [allRequests, activeFilter]);

  // Handle filter change - refetch data if switching to/from "All"
  const handleFilterChange = async (newFilter: FilterType) => {
    const currentIncludeAll = activeFilter === 'all';
    const newIncludeAll = newFilter === 'all';

    // Clear expanded request and patient history when switching filters
    setExpandedRequest(null);
    setPatientHistory([]);
    setPatientData(null);

    setActiveFilter(newFilter);

    // Refetch data if we're changing the includeAll parameter
    if (currentIncludeAll !== newIncludeAll) {
      await fetchRequests(newIncludeAll);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const fetchPatientHistory = async (patientId: string, patientEmail: string) => {
    try {
      setHistoryLoading(true);
      const history = await Promise.all([
        ApiClient.fetchTreatmentPlanByEmail(patientEmail),
        ApiClient.fetchQuestionnaireByEmail(patientId),
        ApiClient.fetchHealthCheckByEmail(patientId),
        ApiClient.fetchPatientOrdersByEmail(patientEmail)
      ]);

      const data = history.flat().filter(item => item !== null && item !== undefined);
      const sortedData = data.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
      setPatientHistory(sortedData);
    } catch (error) {
      console.error('Error fetching patient history:', error);
      setPatientHistory([]);
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleRequestUpdate = async () => {
    // Switch to "Requests" filter after approval/rejection to show pending requests
    setActiveFilter('requests');

    // Always clear expanded state and patient history when a request is processed
    setExpandedRequest(null);
    setPatientHistory([]);
    setPatientData(null);

    // Refresh requests with includeAll=false to show only pending requests
    await fetchRequests(false);
  };

  const handleRequestExpand = async (request: Request) => {
    setExpandedRequest(request);

    // Fetch patient history when request is expanded
    if (request.email && request.patient_id) {
      await fetchPatientHistory(request.patient_id, request.email);

      // Also fetch patient data for SafeScripts
      try {
        const patients = await ApiClient.getPatientsRedis();
        const patient = patients?.find(p => p.patientID === request.patient_id);
        setPatientData(patient);
      } catch (error) {
        console.error('Error fetching patient data:', error);
      }
    }
  };

  const handleRequestCollapse = () => {
    setExpandedRequest(null);
    setPatientHistory([]);
    setPatientData(null);
  };

  const handlePatientMessage = async (patientId: string, patientName: string, channelId?: string, request?: Request) => {
    try {
      // Fetch patient data to get email and state for SafeScripts
      const patients = await ApiClient.getPatientsRedis();
      const patient = patients?.find(p => p.patientID === patientId);

      setSelectedPatient({
        patientId,
        patientName,
        channelId,
        patientEmail: patient?.email,
        patientState: patient?.state,
        request
      });
      setPatientData(patient);
    } catch (error) {
      console.error('Error fetching patient data:', error);
      // Still set selected patient even if we can't fetch additional data
      setSelectedPatient({ patientId, patientName, channelId, request });
    }
  };

  const handleBackToRequests = () => {
    setSelectedPatient(null);
  };

  // Handle notification click - navigate to patient chat
  const handleNotificationClick = async (patientEmail: string) => {
    try {
      console.log(`Looking for patient with email: ${patientEmail}`);
      console.log(`Using doctor accessID: ${doctor?.accessID}`);

      // Use the doctor-specific patient lookup endpoint with correct backend URL

      //get the base url from the env file
      const baseUrl = import.meta.env.VITE_EXPRESS_API_URL || 'http://localhost:5000/api';
      const url = `${baseUrl}/doc/v1.0/patient-by-email/${encodeURIComponent(patientEmail)}`;
      console.log(`Fetching patient from: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log(`Response status: ${response.status}`);

      const result = await response.json();

      if (response.ok && result.success && result.data) {
        const patient = result.data;

        // Create channel ID in the format expected by the chat system
        // Format: d_doctorId_p_patientId
        const channelId = `d_${doctor?.accessID || 'harvest'}_p_${patient.patientID}`;

        // Navigate to patient chat
        setSelectedPatient({
          patientId: patient.patientID,
          patientName: patient.fullName || patient.email || 'Unknown Patient',
          channelId,
          patientEmail: patient.email || patientEmail,
          patientState: patient.state
        });

        setPatientData(patient);

        //console.log(`Navigated to chat for patient: ${patient.fullName || patient.email} (${patientEmail})`);
        enqueueSnackbar(`Opened chat with ${patient.fullName || patient.email}`, { variant: 'success' });
      } else if (response.status === 404) {
        //console.error(`Patient not found for email: ${patientEmail}`);
        enqueueSnackbar(`Patient not found for email: ${patientEmail}`, { variant: 'warning' });
      } else {
        //console.error(`Failed to fetch patient: ${response.status} ${response.statusText}`);
        enqueueSnackbar(`Failed to fetch patient information`, { variant: 'error' });
      }
    } catch (error) {
      console.error('Error navigating to patient chat:', error);

      // Handle specific error cases
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          enqueueSnackbar(`Patient not found for email: ${patientEmail}`, { variant: 'warning' });
        } else if (error.message.includes('401') || error.message.includes('403')) {
          enqueueSnackbar('Authentication required. Please refresh and try again.', { variant: 'error' });
        } else {
          enqueueSnackbar(`Failed to navigate to patient chat: ${error.message}`, { variant: 'error' });
        }
      } else {
        enqueueSnackbar('Failed to navigate to patient chat', { variant: 'error' });
      }
    }
  };

  // Handle View Full History button click
  const handleViewFullHistory = () => {
    if (expandedRequest?.patient_id && expandedRequest?.email) {
      openPatientHistoryDialog(expandedRequest.patient_id, expandedRequest.email);
    }
  };

  // History type definitions for the dialog
  const HistoryTypes = {
    order: {
      name: 'Order',
      element: <Order />
    },
    questionnaire: {
      name: "Questionnaire",
      element: <QuestionnaireForm />
    },
    treatmentplan: {
      name: 'Treatment Plan',
      element: <TreatmentPlan />
    },
    healthCheck: {
      name: 'Health Check - 2 weeks',
      element: <HealthCheck />
    }
  };

  const handleHistoryClicked = (history: any): void => {
    setSelectedPatientHistory(history);
  };

  const getHistoryTypeName = (type: string | undefined): string => {
    if (!type) return 'General';

    if (HistoryTypes[type as keyof typeof HistoryTypes]) {
      return HistoryTypes[type as keyof typeof HistoryTypes].name;
    }

    // Handle chat-treatmentplan case
    if (type?.includes('treatmentplan')) {
      return HistoryTypes['treatmentplan'].name;
    }

    return 'General';
  };

  const getHistoryTypeElement = (type: string | undefined): React.ReactElement | null => {
    if (!type) return null;

    if (HistoryTypes[type as keyof typeof HistoryTypes]) {
      return HistoryTypes[type as keyof typeof HistoryTypes].element;
    }

    // Handle chat-treatmentplan case
    if (type?.includes('treatmentplan')) {
      return HistoryTypes['treatmentplan'].element;
    }

    return null;
  };

  const handleCloseHistoryDialog = () => {
    closePatientHistoryDialog();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  // Show chat view if patient is selected
  if (selectedPatient) {
    return (
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Chat Header with Back Button */}
        <Box sx={{ p: 2, borderBottom: '1px solid #eee', display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={handleBackToRequests} size="small">
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h6">
            Chat with {selectedPatient.patientName}
          </Typography>
        </Box>

        {/* Patient Chat View */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <PatientChatView
            patientId={selectedPatient.patientId}
            patientName={selectedPatient.patientName}
            channelId={selectedPatient.channelId}
            request={selectedPatient.request}
            onRequestUpdate={() => fetchRequests()}
          />
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: '1px solid #eee' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h6">
            Chat
          </Typography>

          {/* Count chips and Chat Notifications */}
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <Chip
            label={`All: ${counts.all}`}
            variant="outlined"
            size="small"
            clickable
            onClick={() => handleFilterChange('all')}
            sx={{
              bgcolor: activeFilter === 'all' ? '#007F00' : 'white',
              border: '2px solid #007F00',
              color: activeFilter === 'all' ? 'white' : '#007F00',
              fontWeight: 'bold',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: activeFilter === 'all' ? '#006600' : '#f8fff8'
              }
            }}
          />
          <Chip
            label={`Requests: ${counts.requests}`}
            variant="outlined"
            size="small"
            clickable
            onClick={() => handleFilterChange('requests')}
            sx={{
              bgcolor: activeFilter === 'requests' ? '#007F00' : 'white',
              border: '2px solid #007F00',
              color: activeFilter === 'requests' ? 'white' : '#007F00',
              fontWeight: 'bold',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: activeFilter === 'requests' ? '#006600' : '#f8fff8'
              }
            }}
          />
          <Chip
            label={`Unread: ${counts.unread}`}
            variant="outlined"
            size="small"
            clickable
            onClick={() => handleFilterChange('unread')}
            sx={{
              bgcolor: activeFilter === 'unread' ? '#007F00' : 'white',
              border: '2px solid #007F00',
              color: activeFilter === 'unread' ? 'white' : '#007F00',
              fontWeight: 'bold',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: activeFilter === 'unread' ? '#006600' : '#f8fff8'
              }
            }}
          />

          {/* Chat Notifications for Doctors */}
          {doctor?.role === 'doctor' && (
            <Box sx={{ ml: 1 }}>
              <DoctorChatNotifications
                variant="inline"
                onNotificationClick={handleNotificationClick}
              />
            </Box>
          )}
          </Box>
        </Box>
      </Box>

      {/* Requests List */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        {filteredRequests.length === 0 ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              {activeFilter === 'all'
                ? 'No requests found'
                : activeFilter === 'unread'
                  ? 'No unread requests'
                  : 'No pending requests'
              }
            </Typography>
          </Box>
        ) : (
          filteredRequests.map((request) => (
            <RequestCard
              key={request.id}
              request={request}
              onUpdate={handleRequestUpdate}
              onPatientMessage={handlePatientMessage}
              onExpand={() => handleRequestExpand(request)}
              onCollapse={handleRequestCollapse}
              isExpanded={expandedRequest?.id === request.id}
            />
          ))
        )}
      </Box>

      {/* Patient History Section - Only show when a request is expanded */}
      {expandedRequest && (
        <Box sx={{ borderTop: '1px solid #eee', p: 2, backgroundColor: '#fafafa' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Typography variant="h6">
              Patient History
            </Typography>

            {/* SafeScripts Button */}
            {patientData?.state && getSafeScriptLink(patientData.state) && (
              <Button
                size="small"
                sx={{
                  backgroundColor: 'grey',
                  color: 'white',
                  textTransform: 'none',
                  py: 0.3,
                  px: 1,
                  fontSize: '0.7rem',
                  minWidth: 'auto'
                }}
                onClick={() => {
                  window.open(
                    `${getSafeScriptLink(patientData.state)}`,
                    "_blank"
                  );
                }}
              >
                {getProvinceAbbreviation(patientData.state)} SafeScript
              </Button>
            )}

            {/* View Full History Button */}
            <Button
              size="small"
              startIcon={<HistoryIcon sx={{ fontSize: '0.9rem' }} />}
              onClick={handleViewFullHistory}
              sx={{
                fontSize: '0.7rem',
                backgroundColor: 'green',
                color: 'white',
                '&:hover': { backgroundColor: 'darkgreen' },
                py: 0.3,
                px: 1,
                minWidth: 'auto',
                textTransform: 'none'
              }}
            >
              View Full History
            </Button>
          </Box>



          {/* Patient History Content */}
          {historyLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : patientHistory.length > 0 ? (
            <Card sx={{ p: 2, bgcolor: 'white' }}>
              <Accordion>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="treatment-plan-content"
                  id="treatment-plan-header"
                >
                  <Typography variant="body1" sx={{ color: '#007F00', fontWeight: 'bold' }}>
                    Last Treatment Plan
                  </Typography>
                  <Typography variant="body2" sx={{ ml: 2, color: 'text.secondary' }}>
                    {patientHistory[0]?.updatedAt && convertTimeStampToReadableFormat(patientHistory[0].updatedAt)}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    {patientHistory.filter(item => item.type === 'treatmentplan').slice(0, 1).map((plan, index) => (
                      <Box key={index}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>Dr Name:</strong> {plan.drName || 'Not specified'}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>Outcome:</strong> {plan.outcome || 'Not specified'}
                        </Typography>

                        {/* Patient Information Copy Buttons */}
                        {expandedRequest && (
                          <Stack
                            direction={{ xs: 'column', sm: 'row' }}
                            spacing={0.3}
                            sx={{ mb: 2, mt: 1 }}
                            useFlexGap
                            flexWrap="wrap"
                          >
                            {expandedRequest.patient_name && (
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyFirstName}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '110px', height: '22px' }}
                              >
                                Copy First Name
                              </Button>
                            )}
                            {expandedRequest.patient_name && (
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyLastName}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '110px', height: '22px' }}
                              >
                                Copy Last Name
                              </Button>
                            )}
                            {(expandedRequest.patient_dob || patientData?.dob) && (
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                onClick={copyDOB}
                                sx={{ textTransform: 'none', fontSize: '0.7rem', py: 0.3, px: 0.8, minWidth: '90px', height: '22px' }}
                              >
                                Copy DOB
                              </Button>
                            )}
                          </Stack>
                        )}

                        {/* 22% Strength Details */}
                        {(plan.dosePerDay22 || plan.maxDosePerDay22 || plan.totalQuantity22) && (
                          <Box sx={{ mt: 2, mb: 2, p: 1, bgcolor: '#f9f9f9', borderRadius: 1 }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                              22% Strength:
                            </Typography>
                            {plan.dosePerDay22 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Dose/Day:</strong> {plan.dosePerDay22}ml
                              </Typography>
                            )}
                            {plan.maxDosePerDay22 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Max Dose/Day:</strong> {plan.maxDosePerDay22}ml
                              </Typography>
                            )}
                            {plan.totalQuantity22 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Quantity:</strong> {plan.totalQuantity22}ml
                              </Typography>
                            )}
                            {plan.numberOfRepeat22 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Repeats:</strong> {plan.numberOfRepeat22}
                              </Typography>
                            )}
                            {plan.supplyInterval22 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Interval:</strong> {plan.supplyInterval22} days
                              </Typography>
                            )}
                          </Box>
                        )}

                        {/* 29% Strength Details */}
                        {(plan.dosePerDay29 || plan.maxDosePerDay29 || plan.totalQuantity29) && (
                          <Box sx={{ mt: 2, mb: 2, p: 1, bgcolor: '#f9f9f9', borderRadius: 1 }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                              29% Strength:
                            </Typography>
                            {plan.dosePerDay29 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Dose/Day:</strong> {plan.dosePerDay29}ml
                              </Typography>
                            )}
                            {plan.maxDosePerDay29 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Max Dose/Day:</strong> {plan.maxDosePerDay29}ml
                              </Typography>
                            )}
                            {plan.totalQuantity29 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Quantity:</strong> {plan.totalQuantity29}ml
                              </Typography>
                            )}
                            {plan.numberOfRepeat29 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Repeats:</strong> {plan.numberOfRepeat29}
                              </Typography>
                            )}
                            {plan.supplyInterval29 && (
                              <Typography variant="body2" sx={{ mb: 0.5 }}>
                                <strong>Interval:</strong> {plan.supplyInterval29} days
                              </Typography>
                            )}
                          </Box>
                        )}

                        {/* Doctor Notes */}
                        {plan.drNotes && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                              Doctor Notes:
                            </Typography>
                            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                              {plan.drNotes}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    ))}
                    {patientHistory.filter(item => item.type === 'treatmentplan').length === 0 && (
                      <Typography variant="body2" color="text.secondary">
                        No treatment plan history available
                      </Typography>
                    )}
                  </Box>
                </AccordionDetails>
              </Accordion>
            </Card>
          ) : (
            <Card sx={{ p: 2, bgcolor: 'white' }}>
              <Typography variant="body2" color="text.secondary" align="center">
                No patient history available
              </Typography>
            </Card>
          )}
        </Box>
      )}

      {/* Patient History Dialog */}
      <Dialog open={isPatientHistoryDialogOpen} fullWidth maxWidth='md' onClose={handleCloseHistoryDialog}>
        <Box sx={{ pt: 5, pl: 5, pr: 5 }}>
          <Grid container justifyContent={'center'} alignItems='center'>
            <Grid sx={{ flexGrow: 1 }}>
              <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                {selectedPatientHistory ? getHistoryTypeName(selectedPatientHistory?.type) : "Patient History"}
              </Typography>
            </Grid>
            <Grid>
              <IconButton onClick={handleCloseHistoryDialog}>
                <CloseIcon />
              </IconButton>
            </Grid>
          </Grid>
        </Box>
        <Box sx={{ padding: 5, overflow: 'auto' }}>
          {/* Loading State */}
          {isPatientHistoryLoading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>Loading patient history...</Typography>
            </Box>
          )}

          {/* Error State */}
          {patientHistoryError && !isPatientHistoryLoading && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {patientHistoryError}
            </Alert>
          )}

          {/* Content - only show when not loading */}
          {!isPatientHistoryLoading && (
            <>
              {selectedPatientHistory && (
                <Button
                  startIcon={<ArrowBackIcon />}
                  onClick={() => setSelectedPatientHistory(undefined)}
                  sx={{ mb: 2 }}
                >
                  Back to History List
                </Button>
              )}
              <Grid sx={{ mt: 2 }}>
                {getHistoryTypeElement(selectedPatientHistory?.type)}
              </Grid>
              {!selectedPatientHistory && (
                <Grid sx={{
                  overflowY: 'auto',
                  maxHeight: '400px',
                  '&::-webkit-scrollbar': {
                    width: '6px',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#cbf5dd',
                    borderRadius: '10px',
                    mt: 1,
                    mb: 1
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: 'green',
                    borderRadius: '10px',
                    p: 2
                  },
                  '&::-webkit-scrollbar-button': {
                    backgroundColor: 'green',
                    height: '7px',
                    borderRadius: '10px'
                  },
                }}>
                  {contextPatientHistory && contextPatientHistory.length > 0 ? (
                    contextPatientHistory.map((history: any, index: number) => (
                      <div key={`history-${index}`} onClick={() => handleHistoryClicked(history)} style={{
                        cursor: 'pointer',
                        width: '100%',
                      }}>
                        <Grid container
                          sx={{
                            boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                            p: 1,
                            mr: '4px',
                            border: '1px solid rgba(0, 0, 0, 0.2)',
                            borderRadius: 2,
                            mb: 1,
                          }}
                          direction={"column"}
                        >
                          <Grid container sx={{ width: '100%' }}>
                            <Grid sx={{ flexGrow: 1, width: '100%' }} container direction={'column'}>
                              <Grid container alignItems={'center'}>
                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                  {getHistoryTypeName(history?.type)}
                                </Typography>
                              </Grid>
                              <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                {history?.updatedAt && convertTimeStampToReadableFormat(history.updatedAt)}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>
                      </div>
                    ))
                  ) : (
                    <Typography sx={{ fontSize: '12px' }} align='center'>No previous history</Typography>
                  )}
                </Grid>
              )}
            </>
          )}
        </Box>
      </Dialog>
    </Box>
  );
};

export default RequestsWindow;
