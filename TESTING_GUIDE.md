# 📋 COMPREHENSIVE TESTING GUIDE: Dynamic Questionnaire Management System

**Date:** [Current Date]  
**Developer:** <PERSON>  
**Branch:** `feat/AddDaynamicQuestionnairesToStaging`  
**Status:** Ready for Testing

---

## 🎯 Feature Overview

I've successfully implemented a comprehensive **Dynamic Questionnaire Management System** that allows administrators to create, configure, and manage patient questionnaires through an intuitive web interface. This system replaces the previous static questionnaire approach with a fully dynamic, section-based management system.

**Key Innovation:** The funnel (patient-facing forms) will now fetch questionnaire configurations dynamically from this system, allowing real-time updates without code deployments.

---

## 📚 Understanding Thresholds

### What is a Threshold?

A **threshold** is the minimum score a patient needs to achieve to be considered **eligible** for a particular treatment or service.

### How Scoring Works:

1. **Patient takes questionnaire** → Answers questions with different point values
2. **System calculates total score** → Adds up points from all answered questions  
3. **Score compared to threshold** → If score ≥ threshold = **APPROVED**, if score < threshold = **REJECTED**

### Example Scenario:
- **Questionnaire**: THC Increase Request
- **Max Score**: 61 points (total possible points)
- **Current Threshold**: 45 points
- **Patient Score**: 52 points
- **Result**: ✅ **APPROVED** (52 ≥ 45)

### Threshold Impact:
- **Low Threshold** (e.g., 30/61): More patients get approved → Easier to qualify
- **High Threshold** (e.g., 55/61): Fewer patients get approved → Stricter requirements

### Why Thresholds Matter:
- **Regulatory Compliance**: Ensures only eligible patients get certain treatments
- **Medical Safety**: Prevents inappropriate access to controlled substances
- **Business Control**: Allows fine-tuning of approval rates
- **Risk Management**: Balances patient access with safety requirements

---

## ✨ Key Features Implemented

### 1. **Admin Management Dashboard**
- **Location:** Admin Panel → Moderation Dashboard → "Questionnaires" Tab
- **Features:**
  - View all questionnaire types with real-time statistics
  - Visual progress indicators showing completion status
  - Quick access to edit questions and thresholds
  - **Toggle Active/Inactive Status** (NEW!)

### 2. **Advanced Questionnaire Editor**
- **Section-Based Organization:** Organize questions into logical sections
- **Question Types Supported:**
  - Radio buttons (multiple choice)
  - Checkboxes (multiple selection)
  - Sliders (range-based input)
  - Text fields (free-form input)
  - Date fields (age validation)
  - Special logic questions (custom scoring)
  - Dropdown menus
- **Real-time Configuration:** Live preview of changes
- **Version Control:** Track questionnaire modifications

### 3. **Dynamic API Endpoints**
- **Funnel Integration:** Patient-facing forms fetch configurations dynamically
- **Real-time Updates:** Changes in admin panel immediately affect funnel forms
- **Version Control:** Track configuration changes and rollback if needed

---

## 🧪 Comprehensive Testing Plan

### **Phase 1: Admin Panel Testing**

#### **1.1 Access Questionnaire Management**
```
Navigation: Admin Panel → Moderation Dashboard → "Questionnaires" Tab
Expected: View list of all questionnaire types with statistics
```

**Test Steps:**
- [ ] Verify all questionnaire types are displayed
- [ ] Check statistics are loading correctly (submissions, approval rate, average score)
- [ ] Confirm visual progress indicators are working
- [ ] Test threshold editing functionality

#### **1.2 Test Toggle Active/Inactive Status** ⭐ **NEW FEATURE**
```
Action: Click toggle switch next to questionnaire status
Expected: Status changes immediately and persists in database
```

**Test Steps:**
- [ ] Verify toggle switch shows current status (Active/Inactive)
- [ ] Test toggling from Active → Inactive
- [ ] Test toggling from Inactive → Active
- [ ] Verify status chip updates (green "Active" vs gray "Inactive")
- [ ] Refresh page and confirm status persists
- [ ] Test multiple questionnaires simultaneously

#### **1.3 Test Questionnaire Editor**
```
Action: Click "Edit Questions" on any questionnaire
Expected: Question editor opens with current configuration
```

**Test Steps:**
- [ ] Verify question list loads correctly
- [ ] Test adding new questions to different sections
- [ ] Test editing existing questions
- [ ] Verify question type selection works
- [ ] Test question deletion functionality
- [ ] Test section management (add/edit/delete sections)

#### **1.4 Test Threshold Management**
```
Action: Click "Edit Threshold" on any questionnaire
Expected: Threshold dialog opens with slider and input field
```

**Test Steps:**
- [ ] Verify current threshold is displayed correctly
- [ ] Test slider functionality (drag to change value)
- [ ] Test manual input field
- [ ] Verify threshold cannot exceed max score
- [ ] Test saving threshold changes
- [ ] Verify changes persist after refresh

---

### **Phase 2: Funnel API Testing** ⭐ **CRITICAL**

#### **2.1 Test Dynamic Questionnaire Configuration API**
```
Endpoint: GET /funnel/v1.0/questionnaires/{type}/config
Purpose: Funnel fetches questionnaire configuration dynamically
```

**Test Scenarios:**

**Scenario A: Active Questionnaire**
```bash
# Test THC Increase questionnaire
curl -X GET "http://localhost:5000/api/funnel/v1.0/questionnaires/thc_increase/config"
```
**Expected Response:**
```json
{
  "success": true,
  "data": {
    "id": "thc_increase",
    "name": "THC Increase",
    "maxScore": 61,
    "threshold": 45,
    "version": "1.0",
    "questions": [...],
    "sections": [...]
  }
}
```

**Scenario B: Inactive Questionnaire**
```bash
# Test inactive questionnaire
curl -X GET "http://localhost:5000/api/funnel/v1.0/questionnaires/inactive_questionnaire/config"
```
**Expected Response:**
```json
{
  "success": false,
  "message": "Questionnaire configuration not found",
  "error": "The requested questionnaire type is not available"
}
```

**Test Steps:**
- [ ] Test all questionnaire types: `thc_increase`, `extend_tp`, `add_22_thc`, `quantity_increase`
- [ ] Verify active questionnaires return full configuration
- [ ] Verify inactive questionnaires return 404 error
- [ ] Test with invalid questionnaire types
- [ ] Verify response structure matches funnel expectations

#### **2.2 Test Real-time Configuration Updates**
```
Workflow: Admin changes configuration → Funnel immediately reflects changes
```

**Test Steps:**
1. **Before Change:**
   - [ ] Call API to get current configuration
   - [ ] Note current threshold value

2. **Make Admin Change:**
   - [ ] Go to admin panel
   - [ ] Change threshold for a questionnaire
   - [ ] Save changes

3. **Verify Real-time Update:**
   - [ ] Immediately call API again
   - [ ] Verify new threshold is returned
   - [ ] Verify no caching issues

#### **2.3 Test Questionnaire Submission Endpoints**
```
Endpoints: POST /funnel/v1.0/patient/{type}-questionnaire
Purpose: Test that submissions work with dynamic configurations
```

**Test Steps:**
- [ ] Submit THC increase questionnaire
- [ ] Submit ExtendTP questionnaire  
- [ ] Submit Add 22% THC questionnaire
- [ ] Submit Quantity increase questionnaire
- [ ] Verify submissions are stored correctly
- [ ] Verify scoring calculations work with dynamic thresholds

---

### **Phase 3: Threshold Impact Testing** ⭐ **BUSINESS CRITICAL**

#### **3.1 Test Threshold Changes Impact**
```
Scenario: Change threshold and verify approval rate changes
```

**Test Steps:**
1. **Baseline Test:**
   - [ ] Note current approval rate in admin panel
   - [ ] Submit test questionnaire with known score

2. **Lower Threshold Test:**
   - [ ] Lower threshold (e.g., 45 → 30)
   - [ ] Submit same test questionnaire
   - [ ] Verify approval rate increases

3. **Higher Threshold Test:**
   - [ ] Raise threshold (e.g., 45 → 55)
   - [ ] Submit same test questionnaire
   - [ ] Verify approval rate decreases

#### **3.2 Test Edge Cases**
```
Scenario: Test boundary conditions and edge cases
```

**Test Steps:**
- [ ] Test threshold = 0 (everyone approved)
- [ ] Test threshold = max score (nobody approved)
- [ ] Test threshold > max score (should be prevented)
- [ ] Test negative threshold (should be prevented)
- [ ] Test decimal thresholds (should be rounded)

---

### **Phase 4: Integration Testing**

#### **4.1 End-to-End Workflow**
```
Complete workflow: Admin configures → Funnel uses → Patient submits → Admin reviews
```

**Test Steps:**
1. **Admin Configuration:**
   - [ ] Create/modify questionnaire in admin panel
   - [ ] Set threshold to specific value
   - [ ] Toggle questionnaire active

2. **Funnel Integration:**
   - [ ] Verify funnel fetches new configuration
   - [ ] Test questionnaire form renders correctly
   - [ ] Submit questionnaire as patient

3. **Result Verification:**
   - [ ] Check submission appears in admin panel
   - [ ] Verify scoring calculation is correct
   - [ ] Verify approval/rejection based on threshold

#### **4.2 Performance Testing**
```
Scenario: Test system performance under load
```

**Test Steps:**
- [ ] Test API response times under normal load
- [ ] Test multiple simultaneous questionnaire submissions
- [ ] Test admin panel performance with many questionnaires
- [ ] Verify no memory leaks or performance degradation

---

## 🔧 Technical Testing Details

### **API Endpoints to Test**

#### **Admin Endpoints:**
```bash
# Get all questionnaire configurations
GET /admin-questionnaire/v1.0/questionnaires

# Get specific questionnaire
GET /admin-questionnaire/v1.0/questionnaires/{id}

# Update questionnaire threshold
PUT /admin-questionnaire/v1.0/questionnaires/{id}/threshold

# Update questionnaire configuration
PUT /admin-questionnaire/v1.0/questionnaires/{id}

# Toggle questionnaire status ⭐ NEW
PUT /admin-questionnaire/v1.0/questionnaires/{id}/status
```

#### **Funnel Endpoints:**
```bash
# Get dynamic questionnaire configuration
GET /funnel/v1.0/questionnaires/{type}/config

# Submit questionnaire responses
POST /funnel/v1.0/patient/thc-increase-questionnaire
POST /funnel/v1.0/patient/extend-tp-questionnaire
POST /funnel/v1.0/patient/add-22-thc-questionnaire
POST /funnel/v1.0/patient/quantity-increase-questionnaire

# Get questionnaire status
GET /funnel/v1.0/patient/thc-increase-questionnaire/status
```

### **Database Verification**
```sql
-- Check questionnaire configurations
SELECT * FROM questionnaire_configs WHERE is_active = true;

-- Check questionnaire sections
SELECT * FROM questionnaire_sections WHERE is_active = true;

-- Check questionnaire submissions
SELECT * FROM thc_increase_questionnaire ORDER BY created_at DESC LIMIT 5;
SELECT * FROM extend_tp_questionnaire ORDER BY created_at DESC LIMIT 5;
SELECT * FROM add_22_thc_questionnaire ORDER BY created_at DESC LIMIT 5;
SELECT * FROM quantity_increase_questionnaire ORDER BY created_at DESC LIMIT 5;
```

---

## ⚠️ Known Limitations & Considerations

1. **Backward Compatibility:** System supports both legacy flat structure and new section-based structure
2. **Special Logic Questions:** Some questions require developer intervention for logic changes
3. **Performance:** Large questionnaires may need pagination in future iterations
4. **Validation:** Client-side validation implemented; server-side validation recommended for production
5. **Funnel Integration:** Funnel frontend must be updated to consume new dynamic API endpoints

---

## 🚀 Success Criteria

### **Must Pass:**
- [ ] All admin panel functionality works correctly
- [ ] Toggle active/inactive status works and persists
- [ ] Threshold changes immediately affect funnel API responses
- [ ] Questionnaire submissions work with dynamic configurations
- [ ] No CORS or API errors
- [ ] Database integrity maintained

### **Should Pass:**
- [ ] Performance remains acceptable under normal load
- [ ] UI is intuitive and user-friendly
- [ ] Error handling is graceful
- [ ] Logging provides adequate debugging information

---

## 📞 Support & Questions

If you encounter any issues during testing:
1. **UI/UX Issues:** Check browser console for errors
2. **API Issues:** Verify network requests in browser dev tools
3. **Data Issues:** Check database connection and migration status
4. **Performance Issues:** Monitor response times and memory usage
5. **Funnel Integration Issues:** Verify API endpoint responses match expected format

**Contact:** Joseph Mojoo  
**Branch:** `feat/AddDaynamicQuestionnairesToStaging`

---

## 🎯 Testing Priority

1. **Priority 1:** Test toggle active/inactive functionality
2. **Priority 2:** Test funnel API endpoints with dynamic configurations
3. **Priority 3:** Test threshold changes and their impact
4. **Priority 4:** Test end-to-end workflow
5. **Priority 5:** Performance and edge case testing

---

**Ready for comprehensive testing! 🎉**

*This system enables real-time questionnaire management without code deployments, significantly improving operational efficiency and patient experience.* 