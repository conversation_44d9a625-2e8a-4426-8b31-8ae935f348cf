# Revised Question Types and Special Cases Documentation

## Question Type Classification

Based on comprehensive analysis of all questionnaire components, here are the actual question types found in the system:

```typescript
type QuestionType = 
  | 'radio'           // Single-select radio buttons
  | 'checkbox'        // Multiple checkboxes  
  | 'slider'          // 1-10 scale sliders
  | 'text'            // Text input (non-scoring)
  | 'special_logic'   // Custom scoring logic
  | 'date'            // Date picker
  | 'dropdown';       // Select dropdowns
```

## Questions by Type and Section Across All Questionnaires

### **Radio Button Questions (Standard Scoring Map)**

#### THC Increase Questionnaire (8 Steps/Sections) - CORRECTED

**Step 1: Current Usage Patterns (3 questions)**
- `consistency` - How consistent were you in using 22% THC flower during the two-week trial? (4 radio options)
- `dosage` - What dosage of 22% THC flower were you taking during the trial? (4 radio options)
- `frequency` - How often did you use 22% THC flower? (4 radio options)

**Step 2: Treatment Assessment (2 questions)**
- `condition` - What condition are you using THC flower to treat? (5 radio options + special logic)
- `effectiveness` - On a scale of 1 to 10, how effective has 22% THC flower been? (5 radio options)

**Step 3: Symptom Management (2 questions)**
- `symptomChanges` - Have you noticed any changes in your symptoms? (4 radio options)
- `sideEffect` - Have you experienced any side effects? (7 radio options + special logic)

**Step 4: Side Effects Assessment (2 questions)**
- `sideEffectManageability` - How manageable were these side effects? (5 radio options)
- `concerns` - Did you have any concerns or issues with your treatment? (2 radio options)

**Step 5: Treatment Effectiveness (2 questions)**
- `treatmentEffectiveness` - Was the 22% THC flower effective in treating your condition? (4 radio options)
- `weaknessAssessment` - Do you feel the 22% THC flower was too weak? (4 radio options)

**Step 6: Relief and Satisfaction (2 questions)**
- `insufficientRelief` - Did you find the 22% THC flower insufficient in providing relief? (4 radio options)
- `satisfactionWithForm` - How satisfied are you with the form of THC flower? (5 radio options)

**Step 7: Future Treatment Preferences (2 questions)**
- `openToHigherPotency` - Would you be open to trying a higher potency? (3 radio options)
- `quickReliefImportance` - How important is it for you to have quick relief? (4 radio options)

**Step 8: Treatment Continuation (2 questions)**
- `continueTreatment` - How likely are you to continue using cannabinoid treatments? (5 radio options)
- `overallSatisfaction` - How satisfied are you with the overall experience? (5 radio options)

#### ExtendTP Questionnaire (12 Steps/Sections - One Question Per Step)

**Step 1: Treatment Adherence**
- `adherence` - Treatment plan adherence

**Step 2: Symptom Improvement**
- `symptomImprovement` - Symptom improvement

**Step 3: Symptom Frequency**
- `symptomFrequency` - Symptom frequency changes

**Step 4: Additional Relief Needs**
- `additionalRelief` - Need for additional relief

**Step 5: Functional Benefits**
- `functionalBenefit` - Functional improvement

**Step 6: Sleep Quality**
- `sleepQuality` - Sleep quality changes

**Step 7: Tolerance Development**
- `tolerance` - Tolerance development

**Step 8: Side Effect Severity**
- `sideEffectSeverity` - Side effect severity

**Step 9: Side Effect Impact**
- `sideEffectTolerability` - Side effect impact

**Step 10: Overall Satisfaction**
- `overallSatisfaction` - Overall satisfaction

**Step 11: Goal Achievement**
- `goalAchievement` - Treatment goal achievement

**Step 12: Future Treatment Intent**
- `treatmentIntent` - Future treatment intention

#### Add 22% THC Questionnaire (5 Steps/Sections) - CORRECTED

**Step 3: Health Changes Assessment**
- `healthChanges` - Recent health changes (radio: no-changes/yes)

**Step 4: Usage Planning**
- `usagePlan` - How would you use 22% THC? (radio: 4 options)

**Step 5: Consent**
- `consent` - Consent to add 22% THC (radio: yes/no)

#### Quantity Increase Questionnaire (5 Steps/Sections) - CORRECTED

**Step 1: Strength Selection and Reasons (8 questions)**
- `thc22Selected` - Checkbox: 22% THC selection
- `thc29Selected` - Checkbox: 29% THC selection
- `thc22RequestedQuantity` - Dropdown: New quantity for 22% THC (conditional)
- `thc29RequestedQuantity` - Dropdown: New quantity for 29% THC (conditional)
- `reasonNotLasting` - Checkbox: Current quantity not lasting full month
- `reasonHigherDoses` - Checkbox: Need more frequent/higher doses
- `reasonTolerance` - Checkbox: Developed tolerance
- `reasonIncreasedSymptoms` - Checkbox: Symptoms have increased
- `reasonOther` - Checkbox: Other reasons

**Step 2: Current Treatment Response (4 questions)**
- `currentEffectiveness` - Slider (1-10): How well current quantity manages symptoms
- `sideEffectsNone` - Checkbox: No side effects
- `sideEffectsMild` - Checkbox: Mild side effects
- `sideEffectsModerate` - Checkbox: Moderate side effects
- `sideEffectsStrong` - Checkbox: Strong side effects
- `usageConsistency` - Radio buttons: Consistency of prescribed amount usage

**Step 3: Health Changes (1 question)**
- `healthChanges` - Radio buttons: Changes in health/medications/lifestyle

**Step 4: Expectations and Preferences (3 questions)**
- `expectations` - Text field: Goals for quantity increase
- `concerns` - Text field: Concerns about increasing quantity
- `intendedUsage` - Radio buttons: How increased quantity will be used

**Step 5: Consent (1 question)**
- `consent` - Radio buttons: Consent for doctor review

#### Initial Patient Questionnaire
- `condition` - Primary medical condition
- `first_medication` - First medication tried
- `second_medication` - Second medication tried
- `children` - Children status
- `disorder` - Mental health disorders
- `diseases` - Other diseases
- `addiction` - Addiction history
- `treatment` - Previous treatments
- `alternative_medecine` - Alternative medicine use
- `trial` - Clinical trial participation
- `gender` - Gender identity

### **Checkbox Questions (Boolean Scoring)**

#### Add 22% THC Questionnaire - CORRECTED

**Step 1: Reasons for Requesting 22% THC**
- `reasonSideEffects` - Experiencing side effects with 29%
- `reasonGentlerEffect` - Want gentler effect
- `reasonDifferentStrain` - Want different strain option
- `reasonTolerance` - Tolerance to current strength
- `reasonOther` - Other reason

**Step 2: Current Side Effects Assessment (Question 2B)**
- `sideEffectsNone` - No side effects
- `sideEffectsMild` - Mild side effects
- `sideEffectsModerate` - Moderate side effects
- `sideEffectsStrong` - Strong side effects

#### Quantity Increase Questionnaire

**Step 1: Reasons for Quantity Increase**
- `reasonNotLasting` - Current amount not lasting
- `reasonHigherDoses` - Need higher doses for effect
- `reasonTolerance` - Developed tolerance
- `reasonIncreasedSymptoms` - Symptoms have increased
- `reasonOther` - Other reason

**Step 2: Side Effects Assessment**
- Various side effect checkboxes (severity levels)

### **Slider Questions (1-10 Scale)**

#### Add 22% THC Questionnaire - CORRECTED

**Step 2: Current Treatment Response (Question 2A)**
- `symptomImprovement` - Symptom improvement rating (1-10 scale)

#### Quantity Increase Questionnaire

**Step 2: Treatment Effectiveness Assessment**
- `currentEffectiveness` - Current treatment effectiveness (1-10 scale)

### **Text Input Questions (Non-scoring)**

#### THC Increase Questionnaire - CORRECTED

**Step 2: Condition Details (Conditional)**
- `conditionOther` - Text field for "other" condition description (appears when "Other" radio selected)

**Step 3: Side Effects Details (Conditional)**
- `sideEffectsOther` - Text field for "other" side effects description (appears when "Other" radio selected)

#### Add 22% THC Questionnaire - CORRECTED

**Step 1: Additional Reason Details (Conditional)**
- `reasonOtherText` - Text description for "other" reason (appears when "Other" checkbox is selected)

**Step 2: Side Effects Details (Conditional)**
- `sideEffectsDescription` - Description of side effects (appears when moderate or strong side effects selected)

**Step 3: Health Changes Details (Conditional)**
- `healthChangesDescription` - Description of health changes (appears when "Yes" to health changes)

**Step 4: Patient Input (2 separate text questions)**
- `expectations` - Patient expectations (multiline text field)
- `concerns` - Patient concerns (multiline text field)

#### Quantity Increase Questionnaire - CORRECTED

**Step 1: Additional Reason Details (Conditional)**
- `reasonOtherText` - Text description for "other" reason (appears when "Other" checkbox is selected)

**Step 2: Side Effects and Usage Details (Conditional)**
- `sideEffectsDescription` - Description of side effects (appears when moderate/strong side effects selected)
- `usageConsistencyOther` - Usage pattern explanation (appears when "other" usage consistency selected)

**Step 3: Health Changes Details (Conditional)**
- `healthChangesDescription` - Description of health changes (appears when "Yes" to health changes)

**Step 4: Patient Input (3 questions)**
- `expectations` - Patient expectations (multiline text field)
- `concerns` - Patient concerns (multiline text field)
- `intendedUsageOther` - Intended usage explanation (appears when "other" intended usage selected)

### **Special Logic Questions (Custom Scoring)**

#### THC Increase Questionnaire - CORRECTED

**Step 2: `condition` - What condition are you using THC flower to treat?**
```typescript
// Special Logic: Any condition except 'other' gets 2 points
if (questionKey === 'condition') {
    return value === 'other' ? 0 : 2;
}
```
- **Logic**: Non-'other' conditions = 2 points, 'other' = 0 points
- **Rationale**: Validates legitimate medical conditions
- **Section**: Treatment Assessment (Q2A)

**Step 3: `sideEffect` - Have you experienced any side effects from using 22% THC flower during the trial?**
```typescript
// Special Logic: 'none' = 4 points, specific effects = 1 point, 'other' = 0
if (questionKey === 'sideEffect') {
    if (value === 'none') return 4;
    if (value === 'other') return 0;
    return 1; // Any specific side effect
}
```
- **Logic**: No side effects = 4 points, known side effects = 1 point, other = 0 points
- **Rationale**: Rewards patients with no side effects, acknowledges known effects
- **Section**: Symptom Management (Q3B)
- **Section**: Symptom Management

### **Date Questions**

#### Initial Patient Questionnaire
- `dob` - Date of birth (Age validation: must be 18+)

### **Dropdown Questions**

#### Initial Patient Questionnaire
- All questions use dropdown/select interfaces but function like radio buttons
- No special dropdown-specific logic identified

## Sectional Structure Summary

### **Section/Step Organization by Questionnaire**

| Questionnaire | Total Steps | Structure | Navigation |
|--------------|-------------|-----------|------------|
| THC Increase | 8 steps | Multi-question steps | Step-by-step with validation |
| ExtendTP | 12 steps | One question per step | Linear progression |
| Add 22% THC | 5 steps | Mixed question types per step | Step-by-step with validation |
| Quantity Increase | 5 steps | Complex first step, then single questions | Step-by-step with validation |
| Initial Questionnaire | Single page | All questions on one page | No step navigation |

### **Section Themes by Questionnaire**

#### THC Increase (Treatment Upgrade Assessment)
1. **Current Usage Patterns** - Baseline usage assessment
2. **Treatment Assessment** - Effectiveness and condition evaluation
3. **Symptom Management** - Symptom changes and side effects
4. **Side Effects Assessment** - Manageability and concerns
5. **Treatment Effectiveness** - Overall effectiveness evaluation
6. **Relief and Satisfaction** - Current treatment satisfaction
7. **Future Treatment Preferences** - Openness to changes
8. **Treatment Continuation** - Long-term treatment intent

#### ExtendTP (Treatment Extension Evaluation)
- Each step focuses on a single aspect of treatment evaluation
- Linear progression through treatment domains
- Comprehensive assessment of current treatment success

#### Add 22% THC (Additional Option Assessment)
1. **Reason Assessment** - Why patient wants 22% option
2. **Current Response** - How patient responds to 29% THC
3. **Health Status** - Recent health changes
4. **Usage Planning** - How 22% would be used
5. **Consent** - Final approval for addition

#### Quantity Increase (Dosage Increase Evaluation)
1. **Comprehensive Request** - Reasons + strength selection + quantities
2. **Treatment Response** - Current effectiveness assessment
3. **Health Changes** - Recent health status changes
4. **Usage Planning** - Expectations and intended usage
5. **Consent** - Final approval for increase

## Admin Interface Implications

### **Section-Based Admin Interface Design**

#### **Questionnaire Section Manager**
- **Section Overview**: Display all sections with question counts and total possible points
- **Section Reordering**: Drag-and-drop section reordering capability
- **Section Themes**: Editable section titles and descriptions
- **Section Validation**: Ensure logical flow between sections
- **Section Analytics**: Show completion rates and drop-off points per section

#### **Section-Specific Editing**
- **Grouped Question Editing**: Edit all questions within a section simultaneously
- **Section Scoring**: View total points possible per section
- **Section Dependencies**: Manage questions that depend on previous section answers
- **Section Validation Rules**: Set requirements for section completion

#### **Multi-Step Navigation Logic**
- **Step Validation Rules**: Configure what makes each step "complete"
- **Conditional Navigation**: Set up skip logic based on previous answers
- **Progress Tracking**: Configure progress bar behavior and milestones
- **Section Timing**: Optional time limits or recommendations per section

### **Question Type Handling in Admin Panel**

#### **Radio Button Editor**
- Standard option list with score inputs
- Validation for score ranges
- Preview of score distribution

#### **Checkbox Editor**
- Boolean true/false scoring inputs
- Multiple selection impact calculator
- Combination scoring preview

#### **Slider Editor**
- Range mapping interface (1-10 to point values)
- Curve adjustment tools
- Visual score distribution

#### **Text Field Manager**
- Mark as non-scoring (0 points)
- Set character limits and validation rules
- Define purpose for medical review

#### **Special Logic Editor**
- **Read-only Logic Display**: Show current logic with explanation
- **Test Interface**: Input test values to see scoring results
- **Developer Flag**: Mark as requiring code changes
- **Warning System**: Alert that changes need technical implementation

```typescript
interface SpecialLogicConfig {
  questionKey: string;
  description: string;
  codeSnippet: string;
  testCases: Array<{
    input: string;
    expectedScore: number;
    description: string;
  }>;
  requiresDeveloper: boolean;
  lastModified: Date;
}
```

#### **Date Field Editor**
- Validation rule settings (min/max age)
- Format specifications
- Required/optional toggle

### **Updated Data Structure with Section Support**

```typescript
interface QuestionnaireConfig {
  id: string;
  name: string;
  maxScore: number;
  threshold: number;
  isActive: boolean;
  version: string;
  sections: SectionConfig[];
  lastModified: Date;
  modifiedBy: string;
}

interface SectionConfig {
  id: string;
  title: string;
  description?: string;
  order: number;
  questions: QuestionConfig[];
  validationRules: SectionValidationRules;
  isActive: boolean;
}

interface SectionValidationRules {
  requireAllQuestions: boolean;
  minimumQuestionsRequired?: number;
  customValidationLogic?: string;
}

interface QuestionConfig {
  key: string;
  text: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'special_logic' | 'date' | 'dropdown';
  sectionId: string; // Links question to its section

  // Standard scoring (radio, checkbox, dropdown)
  answerOptions?: AnswerOption[];

  // Slider-specific
  sliderConfig?: {
    min: number;
    max: number;
    scoreMapping: Record<string, number>;
  };

  // Text field configuration
  textFieldConfig?: {
    maxLength: number;
    required: boolean;
    placeholder: string;
    purpose: string;
  };

  // Special logic configuration
  specialLogic?: SpecialLogicConfig;

  // Date field configuration
  dateConfig?: {
    minAge?: number;
    maxAge?: number;
    required: boolean;
  };

  order: number;
  contributesToScore: boolean;
  isActive: boolean;
  dependsOnQuestion?: string; // For conditional questions
}
```

### **Admin Interface Warnings**

#### **Special Logic Questions**
- **Warning Badge**: "⚠️ Requires Developer"
- **Explanation**: "This question uses custom scoring logic that cannot be modified through the admin interface"
- **Action Options**: "Request Developer Change" button
- **Test Mode**: Allow testing current logic with different inputs

#### **Text Fields**
- **Info Badge**: "ℹ️ Non-scoring"
- **Purpose Display**: Show what the text is used for
- **Character Count**: Live character limit feedback

#### **Dependent Questions**
- **Dependency Warning**: Show which questions depend on others
- **Impact Analysis**: Show how changes affect dependent questions

## Complete Section-to-Question Mapping

### **THC Increase Questionnaire - Complete Section Mapping (CORRECTED)**

| Step | Section Theme | Questions | Question Types | Max Points |
|------|---------------|-----------|----------------|------------|
| 1 | Current Usage Patterns | **Q1A:** `consistency` **Q1B:** `dosage` **Q1C:** `frequency` | Radio (4 options), Radio (4 options), Radio (4 options) | 3+4+3 = 10 |
| 2 | Treatment Assessment | **Q2A:** `condition`, `conditionOther` (conditional) **Q2B:** `effectiveness` | Radio (5 options) + Special Logic, Text (conditional), Radio (5 options) | 2+0+4 = 6 |
| 3 | Symptom Management | **Q3A:** `symptomChanges` **Q3B:** `sideEffect`, `sideEffectsOther` (conditional) | Radio (4 options), Radio (7 options) + Special Logic, Text (conditional) | 4+4+0 = 8 |
| 4 | Side Effects Assessment | **Q4A:** `sideEffectManageability` **Q4B:** `concerns` | Radio (5 options), Radio (2 options) | 4+3 = 7 |
| 5 | Treatment Effectiveness | **Q5A:** `treatmentEffectiveness` **Q5B:** `weaknessAssessment` | Radio (4 options), Radio (4 options) | 4+4 = 8 |
| 6 | Relief and Satisfaction | **Q6A:** `insufficientRelief` **Q6B:** `satisfactionWithForm` | Radio (4 options), Radio (5 options) | 4+4 = 8 |
| 7 | Future Treatment Preferences | **Q7A:** `openToHigherPotency` **Q7B:** `quickReliefImportance` | Radio (3 options), Radio (4 options) | 3+4 = 7 |
| 8 | Treatment Continuation | **Q8A:** `continueTreatment` **Q8B:** `overallSatisfaction` | Radio (5 options), Radio (5 options) | 3+4 = 7 |

**Total: 17 questions (15 scoring, 2 non-scoring), 61 max points, 45+ threshold (73.8%)**

#### **Detailed Question Breakdown:**

**Step 1: Current Usage Patterns (3 questions)**
- **Q1A**: "How consistent were you in using 22% THC flower during the two-week trial?" (4 radio options)
- **Q1B**: "What dosage of 22% THC flower were you taking during the trial?" (4 radio options)
- **Q1C**: "How often did you use 22% THC flower?" (4 radio options)

**Step 2: Treatment Assessment (2 questions + 1 conditional text)**
- **Q2A**: "What condition are you using THC flower to treat?" (5 radio options + conditional text field)
- **Q2B**: "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?" (5 radio options)

**Step 3: Symptom Management (2 questions + 1 conditional text)**
- **Q3A**: "Have you noticed any changes in your symptoms since starting 22% THC flower?" (4 radio options)
- **Q3B**: "Have you experienced any side effects from using 22% THC flower during the trial?" (7 radio options + conditional text field)

**Step 4: Side Effects Assessment (2 questions)**
- **Q4A**: "On a scale of 1 to 10, how manageable were these side effects?" (5 radio options)
- **Q4B**: "Did you have any concerns or issues with your THC flower treatment during the trial?" (2 radio options)

**Step 5: Treatment Effectiveness (2 questions)**
- **Q5A**: "Was the 22% THC flower effective in treating your condition?" (4 radio options)
- **Q5B**: "Do you feel the 22% THC flower was too weak in managing your symptoms?" (4 radio options)

**Step 6: Relief and Satisfaction (2 questions)**
- **Q6A**: "During these breakout pain or acute episodes, did you find the 22% THC flower insufficient in providing relief?" (4 radio options)
- **Q6B**: "How satisfied are you with the form of THC flower you used during the trial?" (5 radio options)

**Step 7: Future Treatment Preferences (2 questions)**
- **Q7A**: "Would you be open to trying a higher potency of THC flower (29%)?" (3 radio options)
- **Q7B**: "How important is it for you to have quick relief from your symptoms?" (4 radio options)

**Step 8: Treatment Continuation (2 questions)**
- **Q8A**: "How likely are you to continue using cannabinoid treatments as part of your treatment plan?" (5 radio options)
- **Q8B**: "How satisfied are you with the overall experience of using 22% THC flower during the trial?" (5 radio options)

### **ExtendTP Questionnaire - Complete Section Mapping**

| Step | Section Theme | Question | Question Type | Max Points |
|------|---------------|----------|---------------|------------|
| 1 | Treatment Adherence | `adherence` | Radio | 5 |
| 2 | Symptom Improvement | `symptomImprovement` | Radio | 5 |
| 3 | Symptom Frequency | `symptomFrequency` | Radio | 5 |
| 4 | Additional Relief Needs | `additionalRelief` | Radio | 5 |
| 5 | Functional Benefits | `functionalBenefit` | Radio | 5 |
| 6 | Sleep Quality | `sleepQuality` | Radio | 5 |
| 7 | Tolerance Development | `tolerance` | Radio | 5 |
| 8 | Side Effect Severity | `sideEffectSeverity` | Radio | 5 |
| 9 | Side Effect Impact | `sideEffectTolerability` | Radio | 5 |
| 10 | Overall Satisfaction | `overallSatisfaction` | Radio | 5 |
| 11 | Goal Achievement | `goalAchievement` | Radio | 5 |
| 12 | Future Treatment Intent | `treatmentIntent` | Radio | 5 |

**Total: 12 questions, 60 max points, 42+ threshold (70%)**

### **Add 22% THC Questionnaire - Complete Section Mapping (CORRECTED)**

| Step | Section Theme | Questions | Question Types | Max Points |
|------|---------------|-----------|----------------|------------|
| 1 | Reasons for Request | `reasonSideEffects`, `reasonGentlerEffect`, `reasonDifferentStrain`, `reasonTolerance`, `reasonOther`, `reasonOtherText` (conditional) | Checkbox×5, Text (conditional) | 4+3+2+3+1+0 = 13 |
| 2 | Current Treatment Response | **Q2A:** `symptomImprovement` **Q2B:** `sideEffectsNone`, `sideEffectsMild`, `sideEffectsModerate`, `sideEffectsStrong`, `sideEffectsDescription` (conditional) | Slider (1-10), Checkbox×4, Text (conditional) | 4+4+2+1+0+0 = 11 |
| 3 | Health Changes Assessment | `healthChanges`, `healthChangesDescription` (conditional) | Radio (2 options), Text (conditional) | 3+0 = 3 |
| 4 | Expectations & Usage Planning | **Q4A:** `expectations` **Q4B:** `concerns` **Q4C:** `usagePlan` | Text (multiline), Text (multiline), Radio (4 options) | 0+0+4 = 4 |
| 5 | Consent | `consent` | Radio (2 options) | 5 |

**Total: 19 questions (13 scoring, 6 non-scoring), 33 max points, 7+ threshold (21.2%)**

#### **Detailed Question Breakdown:**

**Step 1: Reasons for Request**
- Main question: "Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)"
- 5 checkbox options + 1 conditional text field

**Step 2: Current Treatment Response (2 separate questions)**
- **Question 2A**: "How well has your current 29% THC treatment worked for you?" (Slider 1-10)
- **Question 2B**: "Have you experienced any side effects with the 29% THC product? (Select all that apply)" (4 checkboxes + conditional text)

**Step 3: Health Changes Assessment**
- Main question: "Have there been any changes in your health, medications, or lifestyle since your last consultation?"
- Radio buttons: "No changes" vs "Yes — please describe:" + conditional text field

**Step 4: Expectations & Usage Planning (3 separate questions)**
- **Question 4A**: "What do you hope to achieve by adding a 22% THC product?" (Text field)
- **Question 4B**: "Do you have any concerns about adding this option?" (Text field)
- **Question 4C**: "How do you plan to use the 22% THC product alongside your 29%?" (4 radio options)

**Step 5: Consent**
- Main question: "Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?"
- Radio buttons: "Yes" vs "No"

### **Quantity Increase Questionnaire - Complete Section Mapping (CORRECTED)**

| Step | Section Theme | Questions | Question Types | Max Points |
|------|---------------|-----------|----------------|------------|
| 1 | Strength Selection & Reasons | **Q1A:** `thc22Selected`, `thc29Selected`, `thc22RequestedQuantity` (conditional), `thc29RequestedQuantity` (conditional) **Q1B:** `reasonNotLasting`, `reasonHigherDoses`, `reasonTolerance`, `reasonIncreasedSymptoms`, `reasonOther`, `reasonOtherText` (conditional) | Checkbox×2, Dropdown×2, Checkbox×5, Text | 8 total |
| 2 | Treatment Response | **Q2A:** `currentEffectiveness` **Q2B:** `sideEffectsNone`, `sideEffectsMild`, `sideEffectsModerate`, `sideEffectsStrong`, `sideEffectsDescription` (conditional) **Q2C:** `usageConsistency`, `usageConsistencyOther` (conditional) | Slider (1-10), Checkbox×4, Text, Radio, Text | 6+4+6 = 16 |
| 3 | Health Changes | **Q3A:** `healthChanges`, `healthChangesDescription` (conditional) | Radio, Text | 3+0 = 3 |
| 4 | Expectations & Preferences | **Q4A:** `expectations` **Q4B:** `concerns` **Q4C:** `intendedUsage`, `intendedUsageOther` (conditional) | Text, Text, Radio, Text | 0+0+4+0 = 4 |
| 5 | Consent | **Q5A:** `consent` | Radio (Yes/No) | 5 |

**Total: 17 questions (11 scoring, 6 non-scoring), 50 max points, 35+ threshold (70%)**

#### **Quantity Increase - Detailed Question Breakdown:**

**Step 1: Strength Selection & Reasons (8 questions)**
- **Q1A**: "Which strength(s) would you like to increase? (Select all that apply)" (2 checkboxes + 2 conditional dropdowns)
- **Q1B**: "Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan? (Select all that apply)" (5 checkboxes + 1 conditional text)

**Step 2: Current Treatment Response (4 questions)**
- **Q2A**: "On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?" (1-10 slider)
- **Q2B**: "Have you experienced any side effects with your current dose? (Select all that apply)" (4 checkboxes + 1 conditional text)
- **Q2C**: "Have you been using your current prescribed amount consistently?" (radio buttons + conditional text)

**Step 3: Health Changes (1 question)**
- **Q3A**: "Have there been any changes in your health, medications, or lifestyle since your last doctor review?" (radio buttons + conditional text)

**Step 4: Expectations & Preferences (3 questions)**
- **Q4A**: "What do you hope to achieve by increasing the quantity of your medicinal cannabis?" (multiline text)
- **Q4B**: "Do you have any concerns about increasing the quantity?" (multiline text)
- **Q4C**: "How do you intend to use the increased quantity?" (radio buttons + conditional text)

**Step 5: Consent (1 question)**
- **Q5A**: "Do you consent to your doctor reviewing this information and accessing your My Health Record?" (Yes/No radio)

### **Initial Patient Questionnaire - Single Section**

| Section | Questions | Question Types | Validation |
|---------|-----------|----------------|------------|
| Eligibility Assessment | `dob`, `condition`, `first_medication`, `second_medication`, `children`, `disorder`, `diseases`, `addiction`, `treatment`, `alternative_medecine`, `trial`, `gender` | Date, Dropdown×11 | Age 18+, Backend rules |

**Total: 12 questions, No scoring system, Backend validation**

This revised documentation provides a complete picture of all question types and their special handling requirements for the admin interface implementation.
