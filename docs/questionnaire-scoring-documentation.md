# Questionnaire Scoring System Documentation

## Overview

This document provides comprehensive documentation of all questionnaire types, their scoring systems, question structures, and approval thresholds in the Zenith funnel application. This information is intended to support the development of an admin interface for modifying questionnaire scoring systems.

## Questionnaire Types Summary

| Questionnaire Type | Max Score | Threshold | Threshold % | Purpose |
|-------------------|-----------|-----------|-------------|---------|
| THC Increase | 61 | 45 | 73.8% | Upgrade to 29% THC treatment |
| ExtendTP | 60 | 42 | 70% | 6-month treatment plan extension |
| Add 22% THC | 33 | 7 | 21.2% | Add 22% THC option to existing plan |
| Quantity Increase | 50 | 35 | 70% | Increase quantity of current treatment |


## 1. THC Increase Questionnaire

**Purpose**: Determine eligibility for upgrading to 29% THC treatment  
**File**: `src/components/zenith/pages/ThcIncrease.tsx`  
**API Endpoint**: `/funnel/v1.0/patient/thc-increase-questionnaire`

### Scoring Details
- **Maximum Score**: 61 points
- **Eligibility Threshold**: 45 points (73.8%)
- **Question Count**: 17 questions across 8 steps
- **Question Type**: Single-select radio buttons

### Questions and Scoring

#### Step 1: Current Usage Patterns
1. **consistency** - How consistently do you use your current medication?
   - `every-day`: 3 points
   - `most-days`: 2 points
   - `occasionally`: 1 point
   - `rarely`: 0 points

2. **dosage** - What is your typical daily dosage?
   - `less-than-0-5g`: 1 point
   - `0-5g-1g`: 2 points
   - `1g-2g`: 3 points
   - `more-than-2g`: 4 points

3. **frequency** - How often do you use your medication per day?
   - `once-a-day`: 1 point
   - `twice-a-day`: 2 points
   - `three-times-a-day`: 3 points
   - `as-needed`: 1 point

#### Step 2: Treatment Effectiveness
4. **effectiveness** - Rate the effectiveness of your current treatment (1-10 scale)
   - `1-2`: 0 points
   - `3-4`: 1 point
   - `5-6`: 2 points
   - `7-8`: 3 points
   - `9-10`: 4 points

#### Step 3: Symptom Management
5. **condition** - Primary condition being treated (Special scoring)
   - Any condition except 'other': 2 points
   - 'other': 0 points

6. **symptomChanges** - How have your symptoms changed?
   - `significant-improvement`: 4 points
   - `some-improvement`: 2 points
   - `no-change`: 1 point
   - `worsening-symptoms`: 0 points

#### Step 4: Side Effects
7. **sideEffect** - Current side effects (Special scoring)
   - `none`: 4 points
   - Any specific side effect (not 'other'): 1 point
   - 'other': 0 points

8. **sideEffectManageability** - How manageable are side effects? (1-10 scale)
   - `1-2`: 0 points
   - `3-4`: 1 point
   - `5-6`: 2 points
   - `7-8`: 3 points
   - `9-10`: 4 points

9. **concerns** - Do you have concerns about increasing THC?
   - `yes`: 0 points
   - `no`: 3 points

#### Step 5: Treatment Assessment
10. **treatmentEffectiveness** - Overall treatment effectiveness
    - `very-effective`: 4 points
    - `effective`: 3 points
    - `somewhat-effective`: 2 points
    - `not-effective`: 0 points

11. **weaknessAssessment** - Is current treatment too weak?
    - `yes-definitely`: 4 points
    - `yes-somewhat`: 3 points
    - `no-adequate`: 1 point
    - `no-too-strong`: 0 points

#### Step 6: Relief and Satisfaction
12. **insufficientRelief** - Do you experience insufficient relief?
    - `yes-definitely`: 4 points
    - `yes-somewhat`: 3 points
    - `no-adequate`: 1 point
    - `no-complete-relief`: 0 points

13. **satisfactionWithForm** - Satisfaction with current form
    - `very-satisfied`: 4 points
    - `satisfied`: 3 points
    - `neutral`: 2 points
    - `unsatisfied`: 1 point
    - `very-unsatisfied`: 0 points

#### Step 7: Future Treatment
14. **openToHigherPotency** - Open to higher potency?
    - `yes`: 3 points
    - `no`: 0 points
    - `maybe`: 1 point

15. **quickReliefImportance** - Importance of quick relief
    - `very-important`: 4 points
    - `important`: 3 points
    - `neutral`: 2 points
    - `not-important`: 1 point

#### Step 8: Treatment Continuation
16. **continueTreatment** - Likelihood to continue treatment
    - `very-likely`: 3 points
    - `likely`: 2 points
    - `neutral`: 1 point
    - `unlikely`: 0 points
    - `very-unlikely`: 0 points

17. **overallSatisfaction** - Overall satisfaction
    - `very-satisfied`: 4 points
    - `satisfied`: 3 points
    - `neutral`: 2 points
    - `unsatisfied`: 1 point
    - `very-unsatisfied`: 0 points

## 2. ExtendTP Questionnaire

**Purpose**: Determine eligibility for 6-month treatment plan extension  
**File**: `src/components/zenith/pages/ExtendTP.tsx`  
**API Endpoint**: `/funnel/v1.0/patient/extend-tp-questionnaire`

### Scoring Details
- **Maximum Score**: 60 points
- **Eligibility Threshold**: 42 points (70%)
- **Question Count**: 12 questions
- **Question Type**: Single-select radio buttons

### Questions and Scoring

1. **adherence** - Treatment plan adherence
   - `always-followed`: 5 points
   - `usually-followed`: 4 points
   - `sometimes-followed`: 2 points
   - `rarely-followed`: 1 point
   - `never-followed`: 0 points

2. **symptomImprovement** - Symptom improvement
   - `significant-improvement`: 5 points
   - `moderate-improvement`: 4 points
   - `slight-improvement`: 2 points
   - `no-improvement`: 1 point
   - `symptoms-worsened`: 0 points

3. **symptomFrequency** - Symptom frequency changes
   - `much-less-often`: 5 points
   - `somewhat-less-often`: 4 points
   - `about-same`: 2 points
   - `somewhat-more-often`: 1 point
   - `much-more-often`: 0 points

4. **additionalRelief** - Need for additional relief
   - `no-none`: 5 points
   - `rarely`: 4 points
   - `sometimes`: 2 points
   - `frequently`: 0 points

5. **functionalBenefit** - Functional improvement
   - `significantly-improved`: 5 points
   - `somewhat-improved`: 4 points
   - `no-change`: 2 points
   - `somewhat-worsened`: 1 point
   - `significantly-worsened`: 0 points

6. **sleepQuality** - Sleep quality changes
   - `much-improved`: 5 points
   - `somewhat-improved`: 4 points
   - `no-change`: 2 points
   - `somewhat-worse`: 1 point
   - `much-worse`: 0 points

7. **tolerance** - Tolerance development
   - `no-increase-needed`: 5 points
   - `slight-increase`: 4 points
   - `significant-increase`: 2 points
   - `effect-decreased`: 0 points

8. **sideEffectSeverity** - Side effect severity
   - `none-mild`: 5 points
   - `mild`: 4 points
   - `moderate`: 2 points
   - `severe`: 0 points

9. **sideEffectTolerability** - Side effect impact
   - `not-at-all`: 5 points
   - `a-little`: 4 points
   - `moderately`: 2 points
   - `severely`: 0 points

10. **overallSatisfaction** - Overall satisfaction
    - `very-satisfied`: 5 points
    - `somewhat-satisfied`: 4 points
    - `neutral`: 2 points
    - `somewhat-dissatisfied`: 1 point
    - `very-dissatisfied`: 0 points

11. **goalAchievement** - Treatment goal achievement
    - `completely-met`: 5 points
    - `mostly-met`: 4 points
    - `partially-met`: 2 points
    - `not-met`: 0 points

12. **treatmentIntent** - Future treatment intention
    - `continue-current`: 5 points
    - `continue-adjustments`: 4 points
    - `unsure`: 2 points
    - `stop-treatment`: 0 points

## 3. Add 22% THC Questionnaire

**Purpose**: Add 22% THC option to existing 29% THC treatment plan
**File**: `src/components/zenith/pages/Add22Thc.tsx`
**API Endpoint**: `/funnel/v1.0/patient/add-22-thc-questionnaire`

### Scoring Details
- **Maximum Score**: 33 points
- **Eligibility Threshold**: 7 points (21.2%)
- **Question Count**: 5 main questions with sub-questions
- **Question Types**: Checkboxes, radio buttons, sliders

### Questions and Scoring

#### Question 1: Reasons for Requesting 22% THC (Checkboxes)
1. **reasonSideEffects** - Experiencing side effects with 29%
   - `true`: 4 points
   - `false`: 0 points

2. **reasonGentlerEffect** - Want gentler effect
   - `true`: 3 points
   - `false`: 0 points

3. **reasonDifferentStrain** - Want different strain option
   - `true`: 2 points
   - `false`: 0 points

4. **reasonTolerance** - Tolerance to current strength
   - `true`: 3 points
   - `false`: 0 points

5. **reasonOther** - Other reason
   - `true`: 1 point
   - `false`: 0 points

#### Question 2: Current Response to 29% THC
6. **symptomImprovement** - Symptom improvement rating (1-10 scale)
   - `1-2`: 0 points
   - `3-4`: 1 point
   - `5-6`: 2 points
   - `7-8`: 3 points
   - `9-10`: 4 points

7. **sideEffectsNone** - No side effects
   - `true`: 4 points
   - `false`: 0 points

8. **sideEffectsMild** - Mild side effects
   - `true`: 2 points
   - `false`: 0 points

9. **sideEffectsModerate** - Moderate side effects
   - `true`: 1 point
   - `false`: 0 points

10. **sideEffectsStrong** - Strong side effects
    - `true`: 0 points
    - `false`: 0 points

#### Question 3: Health Changes
11. **healthChanges** - Recent health changes
    - `no-changes`: 3 points
    - `yes`: 1 point

#### Question 4: Usage Plan
12. **usagePlan** - How would you use 22% THC?
    - `alternative-situations`: 4 points
    - `rotation-time-symptoms`: 3 points
    - `unsure-advice`: 2 points
    - `other`: 1 point

#### Question 5: Consent
13. **consent** - Consent to add 22% THC
    - `yes`: 5 points
    - `no`: 0 points

## 4. Quantity Increase Questionnaire

**Purpose**: Increase quantity of current THC treatment (22% or 29%)
**File**: `src/components/zenith/pages/QuantityIncrease.tsx`
**API Endpoint**: `/funnel/v1.0/patient/quantity-increase-questionnaire`

### Scoring Details
- **Maximum Score**: 50 points
- **Eligibility Threshold**: 35 points (70%)
- **Question Count**: 8 main questions
- **Question Types**: Checkboxes, radio buttons, sliders, text fields

### Question Weights and Scoring

1. **reasonForRequest** - Reasons for quantity increase (Checkboxes)
   - **Weight**: 8 points maximum
   - Multiple reasons can be selected:
     - `reasonNotLasting`: Variable points
     - `reasonHigherDoses`: Variable points
     - `reasonTolerance`: Variable points
     - `reasonIncreasedSymptoms`: Variable points
     - `reasonOther`: Variable points

2. **currentEffectiveness** - Current treatment effectiveness (1-10 scale)
   - **Weight**: 6 points maximum
   - Scale converted to 0-6 points

3. **sideEffects** - Side effect assessment
   - **Weight**: 4 points (penalty for strong side effects)
   - Checkboxes for different severity levels

4. **usageConsistency** - Usage consistency
   - **Weight**: 6 points
   - Options: `full-amount`, `leftover`, `varies`, `other`

5. **healthChanges** - Health status changes
   - **Weight**: 3 points
   - Options: `no-changes`, `yes`

6. **intendedUsage** - How increased quantity will be used
   - **Weight**: 4 points
   - Multiple options available

7. **expectations** - Patient expectations (Text field)
   - **Weight**: 2 points (or 0 in some implementations)

8. **concerns** - Patient concerns (Text field)
   - **Weight**: 2 points (or 0 in some implementations)

9. **consent** - Consent to quantity increase
   - **Weight**: 5 points
   - `yes`: 5 points, `no`: 0 points

## 5. Initial Patient Questionnaire

**Purpose**: Initial patient eligibility assessment
**File**: `src/components/zenith/forms/FormQuestionaire.tsx`
**API Endpoint**: `/funnel/v1.0/patient/questionnaire/upload`

### Scoring Details
- **No numerical scoring system**
- **Approval Logic**: Based on backend validation rules
- **Question Count**: 12 questions
- **Question Types**: Date picker, dropdowns, radio buttons

### Questions (No Scoring)

1. **dob** - Date of birth (Age validation: must be 18+)
2. **condition** - Primary medical condition
3. **first_medication** - First medication tried
4. **second_medication** - Second medication tried
5. **children** - Children status
6. **disorder** - Mental health disorders
7. **diseases** - Other diseases
8. **addiction** - Addiction history
9. **treatment** - Previous treatments
10. **alternative_medecine** - Alternative medicine use
11. **trial** - Clinical trial participation
12. **gender** - Gender identity

### Approval Logic
- Age validation (18+ required)
- Backend validation rules determine approval
- Results in: `isValid`, `softReject`, or rejection
- No point-based scoring system

## Technical Implementation Details

### Scoring Calculation Methods

#### 1. Standard Scoring Map Pattern
Most questionnaires use a `scoringMap` object structure:
```typescript
const scoringMap: Record<string, Record<string, number>> = {
  questionKey: {
    'answer-value': points,
    'another-answer': points
  }
}
```

#### 2. Special Scoring Logic
Some questions have custom scoring logic:
- **THC Increase - condition**: Any condition except 'other' = 2 points
- **THC Increase - sideEffect**: 'none' = 4 points, specific effects = 1 point
- **Add 22% THC - checkboxes**: Boolean values converted to string keys

#### 3. Score Calculation Flow
1. Iterate through form data
2. Skip non-scoring fields (text descriptions, 'other' text fields)
3. Apply scoring map or special logic
4. Sum individual question scores
5. Compare total against threshold
6. Determine eligibility status

### Database Storage Structure

#### Questionnaire Response Format
```json
{
  "questionsAndAnswers": [
    {
      "questionKey": "consistency",
      "questionText": "How consistently do you use...",
      "answerValue": "every-day",
      "answerText": "Every day",
      "score": 3
    }
  ],
  "totalScore": 45,
  "maxScore": 61,
  "isEligible": true,
  "submittedAt": "2024-01-01T00:00:00.000Z"
}
```

### API Endpoints

| Questionnaire | Submit Endpoint | Status Endpoint |
|--------------|----------------|-----------------|
| THC Increase | `/funnel/v1.0/patient/thc-increase-questionnaire` | `/funnel/v1.0/patient/thc-increase-questionnaire/status` |
| ExtendTP | `/funnel/v1.0/patient/extend-tp-questionnaire` | N/A |
| Add 22% THC | `/funnel/v1.0/patient/add-22-thc-questionnaire` | N/A |
| Quantity Increase | `/funnel/v1.0/patient/quantity-increase-questionnaire` | N/A |
| Initial | `/funnel/v1.0/patient/questionnaire/upload` | N/A |

## Admin Interface Recommendations

### Core Features Required

#### 1. Questionnaire Management
- **List all questionnaire types** with current status
- **View questionnaire details** (questions, scoring, thresholds)
- **Enable/disable questionnaires** temporarily
- **Version control** for questionnaire changes

#### 2. Question Management
- **Add/remove questions** from questionnaires
- **Reorder questions** within questionnaires
- **Edit question text** and descriptions
- **Modify answer options** for multiple choice questions
- **Set question types** (radio, checkbox, slider, text)

#### 3. Scoring System Management
- **Modify individual answer scores** for each question
- **Bulk score updates** across similar questions
- **Score validation** (ensure scores are logical)
- **Preview score impact** before applying changes
- **Score history tracking** for audit purposes

#### 4. Threshold Management
- **Adjust eligibility thresholds** per questionnaire
- **Set percentage-based or absolute thresholds**
- **Multiple threshold levels** (e.g., auto-approve, review, reject)
- **Threshold impact analysis** (how many patients affected)

#### 5. Analytics and Reporting
- **Score distribution analysis** per questionnaire
- **Approval rate tracking** over time
- **Question performance metrics** (which questions discriminate best)
- **Patient outcome correlation** with scores


### Implementation Priority

#### Phase 1: Core Functionality
1. View existing questionnaires and scoring
2. Modify individual question scores
3. Adjust thresholds
4. Basic audit logging

#### Phase 2: Enhanced Management
1. Add/remove questions
2. Bulk operations
3. Advanced analytics
4. Version control



This documentation provides the foundation for building a comprehensive admin interface that allows medical professionals to fine-tune questionnaire scoring systems while maintaining data integrity and regulatory compliance.
